import { Card, CardContent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Clock, Users, Calendar } from 'lucide-react';
import { ScheduleGroup } from '../types';

interface ScheduleGroupsProps {
  scheduleGroups: ScheduleGroup[];
}

export function ScheduleGroups({ scheduleGroups }: ScheduleGroupsProps) {
  const dayOfWeekMap: Record<string, string> = {
    'MONDAY': '월',
    'TUESDAY': '화',
    'WEDNESDAY': '수',
    'THURSDAY': '목',
    'FRIDAY': '금',
    'SATURDAY': '토',
    'SUNDAY': '일'
  };

  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    return `${hours}:${minutes}`;
  };

  return (
    <div className='space-y-4'>
      <div className='flex items-center gap-2 mb-4'>
        <Calendar className='h-5 w-5' />
        <h3 className='text-lg font-semibold'>수업 스케줄</h3>
      </div>
      
      {scheduleGroups.map((group) => (
        <Card key={group.id}>
          <CardHeader>
            <CardTitle className='text-base'>{group.group_name}</CardTitle>
            {group.group_description && (
              <p className='text-sm text-gray-600'>{group.group_description}</p>
            )}
          </CardHeader>
          <CardContent>
            <div className='space-y-3'>
              {/* 그룹 정보 */}
              <div className='flex items-center gap-4 text-sm text-gray-600'>
                <div className='flex items-center gap-1'>
                  <Clock className='h-4 w-4' />
                  <span>주 {group.sessions_per_week}회</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Users className='h-4 w-4' />
                  <span>최대 {group.max_participants}명</span>
                </div>
              </div>
              
              {/* 스케줄 목록 */}
              <div className='space-y-2'>
                {group.schedules.map((schedule) => (
                  <div key={schedule.id} className='flex items-center justify-between p-3 bg-gray-50 rounded-lg'>
                    <div className='flex items-center gap-3'>
                      <Badge variant='outline'>
                        {dayOfWeekMap[schedule.day_of_week]}요일
                      </Badge>
                      <div className='flex items-center gap-1 text-sm'>
                        <Clock className='h-4 w-4' />
                        <span>{formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}</span>
                      </div>
                    </div>
                    <div className='flex items-center gap-2'>
                      <div className='text-sm text-gray-600'>
                        <span className='font-medium text-primary'>{schedule.enrollment_count}</span>
                        <span className='text-gray-500'>/{group.max_participants}</span>
                      </div>
                      <div className='text-xs text-gray-500'>
                        {schedule.occupancy_rate}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}