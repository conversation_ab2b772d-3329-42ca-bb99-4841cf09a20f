'use client';

import { FITNESS_GOALS_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingGoalsStepProps {
  selectedGoals: string[];
  onGoalToggle: (goal: string) => void;
}

export default function OnboardingGoalsStep({
  selectedGoals,
  onGoalToggle,
}: OnboardingGoalsStepProps) {
  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          어떤 목표로 운동을 시작하시나요?
        </h2>
        <p className="text-sm text-gray-600">
          여러 개 선택 가능해요
        </p>
      </div>

      {/* 목표 선택 그리드 */}
      <div className="grid grid-cols-2 gap-4">
        {FITNESS_GOALS_OPTIONS.map((option) => {
          const isSelected = selectedGoals.includes(option.value);
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => onGoalToggle(option.value)}
              className={`
                p-4 border rounded-xl text-left transition-all duration-200
                ${isSelected
                  ? 'bg-indigo-50 border-indigo-600 shadow-sm'
                  : 'bg-white border-gray-200 hover:bg-gray-50'
                }
              `}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                {/* 아이콘 */}
                <div className="text-2xl mb-1">
                  {option.icon}
                </div>
                
                {/* 제목 */}
                <div>
                  <p className={`text-sm font-medium ${
                    isSelected ? 'text-indigo-800' : 'text-gray-900'
                  }`}>
                    {option.label}
                  </p>
                  
                  {/* 설명 */}
                  <p className={`text-xs mt-1 ${
                    isSelected ? 'text-indigo-600' : 'text-gray-500'
                  }`}>
                    {option.description}
                  </p>
                </div>
                
                {/* 선택 인디케이터 */}
                {isSelected && (
                  <div className="w-5 h-5 bg-indigo-600 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
            </button>
          );
        })}
      </div>

      {/* 선택 안내 */}
      {selectedGoals.length === 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            원하는 운동 목표를 선택해주세요
          </p>
        </div>
      )}
      
      {selectedGoals.length > 0 && (
        <div className="bg-indigo-50 rounded-xl p-4">
          <p className="text-sm text-indigo-800 text-center">
            <span className="font-medium">{selectedGoals.length}개 목표</span>를 선택하셨어요
          </p>
        </div>
      )}
    </div>
  );
}