'use client';

import { type SubwayStation } from '@/lib/data/subway-stations';
import SubwayStationSearch from './SubwayStationSearch';

interface OnboardingLocationStepProps {
  selectedStations: SubwayStation[];
  onStationSelect: (station: SubwayStation) => void;
  onStationRemove: (stationId: string) => void;
}

export default function OnboardingLocationStep({
  selectedStations,
  onStationSelect,
  onStationRemove,
}: OnboardingLocationStepProps) {
  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          어느 지역에서 운동하고 싶으신가요?
        </h2>
        <p className="text-sm text-gray-600">
          지하철역 기준으로 선택해주세요
        </p>
      </div>

      {/* 지하철역 검색 컴포넌트 */}
      <SubwayStationSearch
        selectedStations={selectedStations}
        onStationSelect={onStationSelect}
        onStationRemove={onStationRemove}
        placeholder="예: 강남, 홍대입구, 신촌..."
        maxSelections={5}
      />

      {/* 도움말 */}
      <div className="bg-blue-50 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-blue-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-blue-800 mb-1">
              💡 지역 선택 팁
            </p>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• 집이나 직장 근처의 지하철역을 선택해보세요</li>
              <li>• 여러 지역을 선택하면 더 많은 클래스를 추천받을 수 있어요</li>
              <li>• 나중에 설정에서 언제든지 변경할 수 있습니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}