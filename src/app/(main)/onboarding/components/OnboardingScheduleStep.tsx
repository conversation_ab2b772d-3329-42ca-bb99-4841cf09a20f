'use client';

import { DAYS_OPTIONS, TIME_SLOTS_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingScheduleStepProps {
  selectedDays: string[];
  selectedTimeSlots: string[];
  onDayToggle: (day: string) => void;
  onTimeSlotToggle: (timeSlot: string) => void;
}

export default function OnboardingScheduleStep({
  selectedDays,
  selectedTimeSlots,
  onDayToggle,
  onTimeSlotToggle,
}: OnboardingScheduleStepProps) {
  return (
    <div className="space-y-8">
      {/* 헤더 */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          언제 운동하시는 게 좋을까요?
        </h2>
        <p className="text-sm text-gray-600">
          요일과 시간대를 선택해주세요
        </p>
      </div>

      {/* 요일 선택 */}
      <div>
        <h3 className="text-md font-medium text-gray-900 mb-4">
          선호하는 요일 <span className="text-red-500">*</span>
        </h3>
        <div className="grid grid-cols-7 gap-2">
          {DAYS_OPTIONS.map((day) => {
            const isSelected = selectedDays.includes(day.value);
            
            return (
              <button
                key={day.value}
                type="button"
                onClick={() => onDayToggle(day.value)}
                className={`
                  p-3 border rounded-lg text-center transition-all duration-200
                  ${isSelected
                    ? 'bg-indigo-50 border-indigo-600 text-indigo-800'
                    : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <div className="text-xs font-medium">
                  {day.shortLabel}
                </div>
              </button>
            );
          })}
        </div>
        
        {selectedDays.length > 0 && (
          <p className="text-xs text-gray-500 mt-2">
            {selectedDays.length}개 요일 선택됨
          </p>
        )}
      </div>

      {/* 시간대 선택 */}
      <div>
        <h3 className="text-md font-medium text-gray-900 mb-4">
          선호하는 시간대 <span className="text-red-500">*</span>
        </h3>
        <div className="space-y-3">
          {TIME_SLOTS_OPTIONS.map((timeSlot) => {
            const isSelected = selectedTimeSlots.includes(timeSlot.value);
            
            return (
              <button
                key={timeSlot.value}
                type="button"
                onClick={() => onTimeSlotToggle(timeSlot.value)}
                className={`
                  w-full p-4 border rounded-xl text-left transition-all duration-200
                  ${isSelected
                    ? 'bg-indigo-50 border-indigo-600'
                    : 'bg-white border-gray-200 hover:bg-gray-50'
                  }
                `}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="text-xl">
                      {timeSlot.icon}
                    </div>
                    <div>
                      <p className={`text-sm font-medium ${
                        isSelected ? 'text-indigo-800' : 'text-gray-900'
                      }`}>
                        {timeSlot.label}
                      </p>
                      <p className={`text-xs ${
                        isSelected ? 'text-indigo-600' : 'text-gray-500'
                      }`}>
                        {timeSlot.time}
                      </p>
                    </div>
                  </div>
                  
                  {/* 선택 인디케이터 */}
                  <div className={`
                    w-5 h-5 rounded-full border-2 flex items-center justify-center
                    ${isSelected
                      ? 'bg-indigo-600 border-indigo-600'
                      : 'border-gray-300'
                    }
                  `}>
                    {isSelected && (
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                  </div>
                </div>
              </button>
            );
          })}
        </div>
        
        {selectedTimeSlots.length > 0 && (
          <p className="text-xs text-gray-500 mt-2">
            {selectedTimeSlots.length}개 시간대 선택됨
          </p>
        )}
      </div>

      {/* 유연성 안내 */}
      <div className="bg-amber-50 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-amber-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-amber-800 mb-1">
              ⏰ 유연한 스케줄링
            </p>
            <ul className="text-xs text-amber-700 space-y-1">
              <li>• 여러 요일과 시간대를 선택하면 더 많은 옵션을 찾을 수 있어요</li>
              <li>• 선택한 시간대 외에도 유사한 시간의 클래스를 추천해드려요</li>
              <li>• 생활 패턴이 바뀌면 언제든지 수정 가능합니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}