'use client';

import { SPECIALTY_OPTIONS } from '@/lib/constants/onboarding';

interface OnboardingSpecialtyStepProps {
  selectedSpecialties: string[];
  onSpecialtyToggle: (specialty: string) => void;
}

export default function OnboardingSpecialtyStep({
  selectedSpecialties,
  onSpecialtyToggle,
}: OnboardingSpecialtyStepProps) {
  return (
    <div className="space-y-6">
      {/* 헤더 */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          어떤 운동에 관심이 있으신가요?
        </h2>
        <p className="text-sm text-gray-600">
          여러 개 선택 가능해요
        </p>
      </div>

      {/* 운동 종목 선택 그리드 */}
      <div className="grid grid-cols-2 gap-4">
        {SPECIALTY_OPTIONS.map((option) => {
          const isSelected = selectedSpecialties.includes(option.value);
          
          return (
            <button
              key={option.value}
              type="button"
              onClick={() => onSpecialtyToggle(option.value)}
              className={`
                p-4 border rounded-xl text-left transition-all duration-200
                ${isSelected
                  ? 'bg-indigo-50 border-indigo-600 shadow-sm'
                  : 'bg-white border-gray-200 hover:bg-gray-50'
                }
              `}
            >
              <div className="flex flex-col items-center text-center space-y-2">
                {/* 아이콘 */}
                <div className="text-2xl mb-1">
                  {option.icon}
                </div>
                
                {/* 제목 */}
                <div>
                  <p className={`text-sm font-medium ${
                    isSelected ? 'text-indigo-800' : 'text-gray-900'
                  }`}>
                    {option.label}
                  </p>
                  
                  {/* 설명 */}
                  <p className={`text-xs mt-1 ${
                    isSelected ? 'text-indigo-600' : 'text-gray-500'
                  }`}>
                    {option.description}
                  </p>
                </div>
                
                {/* 선택 인디케이터 */}
                {isSelected && (
                  <div className="w-5 h-5 bg-indigo-600 rounded-full flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
            </button>
          );
        })}
      </div>

      {/* 선택 안내 */}
      {selectedSpecialties.length === 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-gray-500">
            관심있는 운동 종목을 선택해주세요
          </p>
        </div>
      )}
      
      {selectedSpecialties.length > 0 && (
        <div className="bg-indigo-50 rounded-xl p-4">
          <p className="text-sm text-indigo-800 text-center">
            <span className="font-medium">{selectedSpecialties.length}개 종목</span>을 선택하셨어요
          </p>
        </div>
      )}

      {/* 추천 정보 */}
      <div className="bg-green-50 rounded-xl p-4">
        <div className="flex items-start space-x-3">
          <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-green-800 mb-1">
              ✨ 맞춤 추천
            </p>
            <ul className="text-xs text-green-700 space-y-1">
              <li>• 선택한 운동 종목에 맞는 클래스를 우선 추천해드려요</li>
              <li>• 초보자도 쉽게 시작할 수 있는 입문 클래스도 있어요</li>
              <li>• 관심사가 바뀌면 언제든지 수정할 수 있습니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}