'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  ONBOARDING_STEPS,
  ONBOARDING_STEP_INFO,
  STORAGE_KEYS,
  type OnboardingData,
} from '@/lib/constants/onboarding';
import { getCurrentUser } from '@/lib/supabase/auth';
import { type SubwayStation } from '@/lib/data/subway-stations';
import StepProgress from './components/StepProgress';
import OnboardingGoalsStep from './components/OnboardingGoalsStep';
import OnboardingLocationStep from './components/OnboardingLocationStep';
import OnboardingGenderStep from './components/OnboardingGenderStep';
import OnboardingSpecialtyStep from './components/OnboardingSpecialtyStep';
import OnboardingScheduleStep from './components/OnboardingScheduleStep';
import OnboardingLevelStep from './components/OnboardingLevelStep';

export default function OnboardingPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    fitnessGoals: [],
    preferredStations: [],
    preferredSpecialties: [],
    preferredDays: [],
    preferredTimeSlots: [],
  });

  // 페이지 로드 시 로컬 데이터 복구
  useEffect(() => {
    loadFromLocalStorage();
  }, []);

  // 로컬 스토리지에서 데이터 복구
  const loadFromLocalStorage = () => {
    try {
      const savedData = localStorage.getItem(STORAGE_KEYS.ONBOARDING_DATA);
      const savedStep = localStorage.getItem(STORAGE_KEYS.CURRENT_STEP);

      if (savedData) {
        const parsedData = JSON.parse(savedData);
        setOnboardingData(parsedData);
      }

      if (savedStep) {
        setCurrentStep(parseInt(savedStep, 10));
      }
    } catch (error) {
      console.error('로컬 스토리지 복구 오류:', error);
    }
  };

  // 로컬 스토리지에 데이터 저장
  const saveToLocalStorage = (data: OnboardingData, step: number) => {
    try {
      localStorage.setItem(STORAGE_KEYS.ONBOARDING_DATA, JSON.stringify(data));
      localStorage.setItem(STORAGE_KEYS.CURRENT_STEP, step.toString());
    } catch (error) {
      console.error('로컬 스토리지 저장 오류:', error);
    }
  };

  // 로컬 스토리지 클리어
  const clearLocalStorage = () => {
    try {
      localStorage.removeItem(STORAGE_KEYS.ONBOARDING_DATA);
      localStorage.removeItem(STORAGE_KEYS.CURRENT_STEP);
    } catch (error) {
      console.error('로컬 스토리지 클리어 오류:', error);
    }
  };

  // 다음 단계로 이동
  const handleNext = async () => {
    const newStep = currentStep + 1;
    setCurrentStep(newStep);
    saveToLocalStorage(onboardingData, newStep);

    // 마지막 단계면 온보딩 완료 처리
    if (newStep > Object.keys(ONBOARDING_STEPS).length) {
      await handleCompleteOnboarding();
    }
  };

  // 현재 단계 건너뛰기
  const handleSkip = async () => {
    await handleNext();
  };

  // 전체 온보딩 건너뛰기
  const handleSkipAll = () => {
    try {
      // 로컬스토리지에 완료 상태 저장
      localStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');
      // 온보딩 관련 로컬 데이터 클리어
      clearLocalStorage();
      // 메인 페이지로 이동
      router.push('/');
    } catch (error) {
      console.error('온보딩 스킵 오류:', error);
    }
  };

  // 이전 단계로 이동
  const handlePrevious = () => {
    const newStep = Math.max(1, currentStep - 1);
    setCurrentStep(newStep);
    saveToLocalStorage(onboardingData, newStep);
  };

  // 온보딩 완료 처리
  const handleCompleteOnboarding = async () => {
    setIsLoading(true);

    try {
      // 로컬스토리지에 완료 상태 저장
      localStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, 'true');

      // 로그인된 사용자만 서버에 데이터 저장
      const user = await getCurrentUser();
      if (user) {
        try {
          const response = await fetch('/api/member/onboarding', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(onboardingData),
          });

          const result = await response.json();
          if (!response.ok || !result.success) {
            console.warn('서버 저장 실패:', result.error);
            // 서버 저장 실패해도 로컬 완료 상태는 유지
          }
        } catch (serverError) {
          console.warn('서버 저장 오류:', serverError);
          // 서버 저장 실패해도 로컬 완료 상태는 유지
        }
      }

      // 로컬 스토리지 온보딩 데이터 클리어
      clearLocalStorage();

      // 메인 페이지로 이동
      router.push('/');
    } catch (error) {
      console.error('온보딩 완료 오류:', error);
      alert('온보딩 완료 중 오류가 발생했습니다. 다시 시도해주세요.');
    } finally {
      setIsLoading(false);
    }
  };

  // 단계별 데이터 업데이트 함수들
  const updateOnboardingData = (updates: Partial<OnboardingData>) => {
    const newData = { ...onboardingData, ...updates };
    setOnboardingData(newData);
    saveToLocalStorage(newData, currentStep);
  };

  // Step 1: 운동 목표 관련
  const handleGoalToggle = (goal: string) => {
    const currentGoals = onboardingData.fitnessGoals || [];
    const newGoals = currentGoals.includes(goal)
      ? currentGoals.filter(g => g !== goal)
      : [...currentGoals, goal];
    updateOnboardingData({ fitnessGoals: newGoals });
  };

  // Step 2: 지역 관련
  const handleStationSelect = (station: SubwayStation) => {
    const newStations = [...(onboardingData.preferredStations || []), station];
    updateOnboardingData({ preferredStations: newStations });
  };

  const handleStationRemove = (stationId: string) => {
    const newStations = (onboardingData.preferredStations || []).filter(
      s => s.id !== stationId
    );
    updateOnboardingData({ preferredStations: newStations });
  };

  // Step 3: 성별 관련
  const handleGenderSelect = (gender: string) => {
    updateOnboardingData({ gender: gender as any });
  };

  // Step 4: 운동 종목 관련
  const handleSpecialtyToggle = (specialty: string) => {
    const currentSpecialties = onboardingData.preferredSpecialties || [];
    const newSpecialties = currentSpecialties.includes(specialty)
      ? currentSpecialties.filter(s => s !== specialty)
      : [...currentSpecialties, specialty];
    updateOnboardingData({ preferredSpecialties: newSpecialties });
  };

  // Step 5: 스케줄 관련
  const handleDayToggle = (day: string) => {
    const currentDays = onboardingData.preferredDays || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day];
    updateOnboardingData({ preferredDays: newDays });
  };

  const handleTimeSlotToggle = (timeSlot: string) => {
    const currentTimeSlots = onboardingData.preferredTimeSlots || [];
    const newTimeSlots = currentTimeSlots.includes(timeSlot)
      ? currentTimeSlots.filter(t => t !== timeSlot)
      : [...currentTimeSlots, timeSlot];
    updateOnboardingData({ preferredTimeSlots: newTimeSlots });
  };

  // Step 6: 수준 관련
  const handleLevelSelect = (level: string) => {
    updateOnboardingData({ fitnessLevel: level as any });
  };

  // 현재 단계의 정보
  const currentStepInfo =
    ONBOARDING_STEP_INFO[currentStep as keyof typeof ONBOARDING_STEP_INFO];
  const totalSteps = Object.keys(ONBOARDING_STEPS).length;
  const isLastStep = currentStep === totalSteps;
  const isFirstStep = currentStep === 1;

  return (
    <div className='min-h-screen bg-gray-50'>
      {/* 헤더 */}
      <div className='border-b border-gray-100 bg-white'>
        <div className='mx-auto max-w-md px-4 py-4'>
          <div className='flex items-center justify-between'>
            <button onClick={() => router.back()} className='p-2'>
              <svg
                className='h-6 w-6 text-gray-600'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth={2}
                  d='M15 19l-7-7 7-7'
                />
              </svg>
            </button>
            <h1 className='text-lg font-semibold text-gray-900'>
              {currentStepInfo?.title}
            </h1>
            <button
              onClick={handleSkipAll}
              className='text-sm text-gray-500 transition-colors hover:text-gray-700'
              disabled={isLoading}
            >
              모두 건너뛰기
            </button>
          </div>
        </div>
      </div>

      <div className='mx-auto max-w-md px-4 py-6'>
        {/* 진행률 표시 */}
        <StepProgress currentStep={currentStep} totalSteps={totalSteps} />

        {/* 단계별 컨텐츠 */}
        <div className='rounded-xl bg-white p-6 shadow-sm'>
          {currentStep === ONBOARDING_STEPS.GOALS && (
            <OnboardingGoalsStep
              selectedGoals={onboardingData.fitnessGoals || []}
              onGoalToggle={handleGoalToggle}
            />
          )}

          {currentStep === ONBOARDING_STEPS.LOCATION && (
            <OnboardingLocationStep
              selectedStations={onboardingData.preferredStations || []}
              onStationSelect={handleStationSelect}
              onStationRemove={handleStationRemove}
            />
          )}

          {currentStep === ONBOARDING_STEPS.GENDER && (
            <OnboardingGenderStep
              selectedGender={onboardingData.gender || ''}
              onGenderSelect={handleGenderSelect}
            />
          )}

          {currentStep === ONBOARDING_STEPS.SPECIALTY && (
            <OnboardingSpecialtyStep
              selectedSpecialties={onboardingData.preferredSpecialties || []}
              onSpecialtyToggle={handleSpecialtyToggle}
            />
          )}

          {currentStep === ONBOARDING_STEPS.SCHEDULE && (
            <OnboardingScheduleStep
              selectedDays={onboardingData.preferredDays || []}
              selectedTimeSlots={onboardingData.preferredTimeSlots || []}
              onDayToggle={handleDayToggle}
              onTimeSlotToggle={handleTimeSlotToggle}
            />
          )}

          {currentStep === ONBOARDING_STEPS.LEVEL && (
            <OnboardingLevelStep
              selectedLevel={onboardingData.fitnessLevel || ''}
              onLevelSelect={handleLevelSelect}
            />
          )}
        </div>

        {/* 네비게이션 버튼 */}
        <div className='mt-6 space-y-3'>
          <button
            onClick={isLastStep ? handleCompleteOnboarding : handleNext}
            disabled={isLoading}
            className='flex w-full items-center justify-center rounded-xl bg-indigo-600 px-6 py-4 text-base font-medium text-white transition-colors hover:bg-indigo-700 disabled:cursor-not-allowed disabled:opacity-50'
          >
            {isLoading ? (
              <>
                <div className='mr-2 h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent'></div>
                완료 중...
              </>
            ) : isLastStep ? (
              '온보딩 완료'
            ) : (
              '다음 단계'
            )}
          </button>

          <div className='flex space-x-3'>
            {!isLastStep && (
              <button
                onClick={handleSkip}
                disabled={isLoading}
                className='flex-1 rounded-xl bg-gray-100 px-6 py-3 text-base font-medium text-gray-700 transition-colors hover:bg-gray-200 disabled:opacity-50'
              >
                이 단계 건너뛰기
              </button>
            )}

            {!isFirstStep && (
              <button
                onClick={handlePrevious}
                disabled={isLoading}
                className='flex-1 rounded-xl bg-gray-100 px-6 py-3 text-base font-medium text-gray-700 transition-colors hover:bg-gray-200 disabled:opacity-50'
              >
                이전 단계
              </button>
            )}
          </div>

          {/* 전체 건너뛰기 버튼 */}
          <button
            onClick={handleSkipAll}
            disabled={isLoading}
            className='w-full rounded-xl border border-gray-300 bg-transparent px-6 py-2 text-sm font-medium text-gray-500 transition-colors hover:border-gray-400 hover:text-gray-700 disabled:opacity-50'
          >
            온보딩을 나중에 하고 바로 시작하기
          </button>
        </div>

        {/* 진행 상황 텍스트 */}
        <div className='mt-4 text-center'>
          <p className='text-sm text-gray-500'>{currentStepInfo?.subtitle}</p>
        </div>
      </div>
    </div>
  );
}
