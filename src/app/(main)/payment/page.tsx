'use client';

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { CreditCard } from 'lucide-react';

interface PaymentMethod {
  id: string;
  name: string;
  logo?: string;
  selected?: boolean;
}

function PaymentPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Get class data from URL parameters
  const classData = {
    title: searchParams.get('title') || '운동 경험이 없는 초보자...',
    coach: searchParams.get('coach') || '최인정 코치님',
    center: '턴온피트니스센터',
    location: searchParams.get('location') || '공덕역 1번 출구에서 5분',
    schedule: searchParams.get('schedule') || '1개월, 8회 수업',
    sessions: '1개월, 8회 수업',
    monthlyFee: parseInt(searchParams.get('totalPrice') || '200000'),
    depositAmount: parseInt(searchParams.get('depositAmount') || '30000'),
    depositRate: parseInt(searchParams.get('depositRate') || '15'),
    promotionDiscount: 20000,
  };

  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<string>('credit-card');
  const [couponCode, setCouponCode] = useState<string>('');
  const [agreements, setAgreements] = useState({
    orderConfirm: false,
    personalInfo: false,
  });
  const [isProcessing, setIsProcessing] = useState(false);

  const paymentMethods: PaymentMethod[] = [
    { id: 'credit-card', name: '신용·체크카드' },
    { id: 'kakao-pay', name: 'KakaoPay' },
    { id: 'toss-pay', name: 'TossPay' },
    { id: 'naver-pay', name: 'NaverPay' },
  ];

  const finalAmount = classData.depositAmount - classData.promotionDiscount;
  const allAgreementsChecked = Object.values(agreements).every(Boolean);

  const handlePayment = async () => {
    if (!allAgreementsChecked) {
      alert('모든 약관에 동의해주세요.');
      return;
    }

    setIsProcessing(true);

    // Simulate payment processing
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Navigate to success page or show success modal
    router.push('/payment/success?transactionId=12345&enrollmentId=67890');
  };

  const handleCouponApply = () => {
    // Implement coupon application logic
    console.log('Apply coupon:', couponCode);
  };

  return (
    <div className='min-h-screen bg-white'>
      <div className='mx-auto max-w-md space-y-6 p-4'>
        {/* Main Message */}
        <div className='text-left'>
          <h2 className='text-xl font-bold text-black'>
            쉘위 그룹 수업 확정을 위해 <br />
            <span className='text-primary font-bold'>예약금 결제</span>
            해주세요.
          </h2>
        </div>
        <div>
          <p>예약금</p>
        </div>

        {/* Location Info */}
        {/* <p className='text-center text-gray-600'>
          {classData.center} · {classData.location}
        </p> */}

        {/* Class Information */}
        <h3 className='mb-3 text-xl font-semibold text-black'>수업 정보</h3>
        <Card>
          <CardContent className='p-4'>
            <div className='space-y-3'>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업내용</span>
                <span className='max-w-[200px] truncate text-right'>
                  {classData.title}
                </span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업코치</span>
                <span>{classData.coach}</span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>수업센터</span>
                <span>{classData.center}</span>
              </div>
              <div className='flex justify-between'>
                <span className='font-semibold text-black'>PT 횟수</span>
                <span>{classData.sessions}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods */}
        <h3 className='mb-3 text-xl font-semibold text-black'>결제수단</h3>
        <Card>
          <CardContent className='p-4'>
            <div className='space-y-2'>
              {paymentMethods.map(method => (
                <button
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                  className={`flex w-full items-center justify-between rounded-lg border p-3 text-left ${
                    selectedPaymentMethod === method.id
                      ? 'border-primary bg-purple-50'
                      : 'border-gray-200'
                  }`}
                >
                  <span>{method.name}</span>
                  {method.id === 'kakao-pay' && (
                    <div className='rounded bg-yellow-400 px-2 py-1 text-xs font-bold'>
                      pay
                    </div>
                  )}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Coupon Section */}
        {/* <h3 className='mb-3 text-xl font-semibold text-black'>쿠폰</h3>
        <Card>
          <CardContent className='p-4'>
            <div className='flex gap-2'>
              <Input
                placeholder='사용 가능한 쿠폰이 있어요.'
                value={couponCode}
                onChange={e => setCouponCode(e.target.value)}
                className='flex-1'
              />
              <Button
                variant='outline'
                onClick={handleCouponApply}
                className='whitespace-nowrap'
              >
                프로모션 코드 등록하기
              </Button>
            </div>
          </CardContent>
        </Card> */}

        {/* Payment Information */}
        <h3 className='mb-3 text-xl font-semibold text-black'>결제 정보</h3>
        <Card>
          <CardContent className='p-4'>
            <div className='space-y-3'>
              <div className='flex justify-between text-lg font-semibold'>
                <span>최종 결제 금액</span>
                <span className='text-primary'>
                  {finalAmount.toLocaleString()}원
                </span>
              </div>

              <Separator />

              <div className='space-y-2 text-xs'>
                <div className='flex justify-between'>
                  <span className='font-bold text-black'>
                    예약금 결제 (수업료 {classData.depositRate}%)
                  </span>
                  <span className='font-bold text-black'>
                    {classData.depositAmount.toLocaleString()}원
                  </span>
                </div>
                <div className='flex justify-between pl-5'>
                  <span className='text-gray-500'>월 수업료</span>
                  <span>{classData.monthlyFee.toLocaleString()}원</span>
                </div>
                <div className='flex justify-between'>
                  <span className='font-bold text-black'>프로모션 적용가</span>
                  <span className='text-primary font-bold'>
                    -{classData.promotionDiscount.toLocaleString()}원
                  </span>
                </div>
                <div className='flex justify-between pl-5 text-xs text-gray-500'>
                  <span>센터 현장 결제</span>
                  <span>-{classData.promotionDiscount.toLocaleString()}원</span>
                </div>
              </div>

              <div className='space-y-1 text-xs text-gray-500'>
                <div className='flex items-start gap-2'>
                  <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                  <span>
                    예약금 15% 제외한 전액 수업료는 센터에서 결제해주시면
                    됩니다.
                  </span>
                </div>
                <div className='flex items-start gap-2'>
                  <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                  <span>
                    첫월 수업료 수업의 취소는 전액 2주 이내로 완불되어야 합니다.
                  </span>
                </div>
                <div className='flex items-start gap-2'>
                  <div className='bg-primary mt-2 h-1 w-1 flex-shrink-0 rounded-full' />
                  <span>수업 확정 후 취소 시 완불 정책에 따라 처리됩니다.</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Agreements */}
        <div className='space-y-2'>
          <div className='flex items-center gap-3'>
            <Checkbox
              id='order-confirm'
              checked={agreements.orderConfirm}
              onCheckedChange={checked =>
                setAgreements(prev => ({
                  ...prev,
                  orderConfirm: checked as boolean,
                }))
              }
            />
            <label htmlFor='order-confirm' className='flex-1 text-sm'>
              주문 내용을 확인하였으며 결제에 동의합니다.
            </label>
            <button className='text-primary underline'>자세히 보기</button>
          </div>

          <div className='flex items-center gap-3'>
            <Checkbox
              id='personal-info'
              checked={agreements.personalInfo}
              onCheckedChange={checked =>
                setAgreements(prev => ({
                  ...prev,
                  personalInfo: checked as boolean,
                }))
              }
            />
            <label htmlFor='personal-info' className='flex-1 text-sm'>
              개인정보 제3자 제공 내용을 확인하였으며 동의합니다.
            </label>
            <button className='text-primary underline'>자세히 보기</button>
          </div>

          <div className='rounded-md bg-gray-50 p-3 text-xs text-gray-600'>
            쉘위는 수업 운영을 위해 고객님의 정보를 파트너 센터에 제공합니다.
            고객님의 개인정보는 안전하게 처리되며, 자세한 내용은 약관에서
            확인하실 수 있습니다.
          </div>
        </div>

        {/* Payment Button */}
        <Button
          size='lg'
          onClick={handlePayment}
          disabled={!allAgreementsChecked || isProcessing}
          className='bg-primary hover:bg-primary/90 w-full'
        >
          {isProcessing ? (
            '결제 중...'
          ) : (
            <>
              <CreditCard className='mr-2 h-4 w-4' />
              {finalAmount.toLocaleString()}원 결제하기
            </>
          )}
        </Button>
      </div>
    </div>
  );
}

function PaymentPageFallback() {
  return (
    <div className='flex min-h-screen items-center justify-center bg-gray-50'>
      <div className='text-center'>
        <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent'></div>
        <p className='text-sm text-gray-600'>결제 정보를 불러오는 중...</p>
      </div>
    </div>
  );
}

export default function PaymentPage() {
  return (
    <Suspense fallback={<PaymentPageFallback />}>
      <PaymentPageContent />
    </Suspense>
  );
}
