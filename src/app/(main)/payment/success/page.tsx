import Link from 'next/link';
import { redirect } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Home } from 'lucide-react';
import { paymentServerApi } from '@/lib/api/payment-server';

interface PaymentSuccessPageProps {
  searchParams: Promise<{
    enrollmentId?: string;
    transactionId?: string;
  }>;
}

function formatPaymentMethod(method: string): string {
  const methodMap: Record<string, string> = {
    credit_card: '신용·체크카드',
    debit_card: '체크카드',
    bank_transfer: '계좌이체',
    mobile_payment: '휴대폰결제',
    kakaopay: '카카오페이',
    naverpay: '네이버페이',
    payco: '페이코',
  };
  return methodMap[method] || method;
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ko-KR').format(amount) + '원';
}

function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('ko-KR');
}

export default async function PaymentSuccessPage({
  searchParams,
}: PaymentSuccessPageProps) {
  const params = await searchParams;
  const { enrollmentId, transactionId } = params;

  if (!enrollmentId) {
    redirect('/payment');
  }

  try {
    const paymentData = await paymentServerApi.getPaymentSuccessData({
      enrollmentId,
      transactionId,
    });

    return (
      <div className='flex min-h-screen flex-col bg-white'>
        <div className='flex flex-1 flex-col p-4'>
          <div className='space-y-6 text-center'>
            {/* Success Icon */}
            <div className='bg-primary mx-auto flex size-20 items-center justify-center rounded-full'>
              <CheckCircle className='size-14 text-white' />
            </div>

            {/* Success Message */}
            <div className='space-y-2'>
              <h2 className='text-2xl font-bold text-black'>
                예약금 결제가 완료되었습니다!
              </h2>
              <p className='text-gray-600'>
                {paymentData.className} 예약이 확정되었어요.
              </p>
            </div>

            {/* Class Information */}
            <Card>
              <CardContent className='p-4'>
                <h3 className='mb-3 font-semibold text-black'>수업 정보</h3>
                <div className='space-y-2'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>수업명</span>
                    <span>{paymentData.className}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>강사</span>
                    <span>{paymentData.instructorName}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>센터</span>
                    <span>{paymentData.studioName}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>시간대</span>
                    <span>{paymentData.scheduleGroupName}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Details */}
            <Card>
              <CardContent className='p-4'>
                <h3 className='mb-3 font-semibold text-black'>결제 정보</h3>
                <div className='space-y-3'>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>결제 방법</span>
                    <span>{formatPaymentMethod(paymentData.paymentMethod)}</span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>결제 금액</span>
                    <span className='text-primary font-semibold'>
                      {formatCurrency(paymentData.paymentAmount)}
                    </span>
                  </div>
                  <div className='flex justify-between'>
                    <span className='text-gray-600'>결제 일시</span>
                    <span>{formatDate(paymentData.paymentDate)}</span>
                  </div>
                  {paymentData.remainingAmount && paymentData.remainingAmount > 0 && (
                    <div className='flex justify-between'>
                      <span className='text-gray-600'>남은 수업료</span>
                      <span className='text-orange-600 font-semibold'>
                        {formatCurrency(paymentData.remainingAmount)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card>
              <CardContent className='p-4'>
                <h3 className='mb-4 font-semibold text-black'>다음 단계</h3>
                <div className='space-y-1 text-sm text-gray-500'>
                  {paymentData.nextSteps.map((step, index) => (
                    <div key={index} className='flex items-start gap-2'>
                      <div
                        className={`mt-2 h-1 w-1 flex-shrink-0 rounded-full ${
                          step.completed ? 'bg-green-500' : 'bg-primary'
                        }`}
                      />
                      <span className={step.completed ? 'text-green-600' : ''}>
                        {step.description}
                      </span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className='space-y-3'>
              <Button size='lg' asChild className='bg-primary w-full'>
                <Link href='/profile/bookings'>내 예약 확인하기</Link>
              </Button>
              <Button size='lg' variant='outline' asChild className='w-full'>
                <Link href='/'>
                  <Home className='mr-2 h-4 w-4' />
                  홈으로 돌아가기
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading payment success data:', error);
    redirect('/payment?error=payment_not_found');
  }
}
