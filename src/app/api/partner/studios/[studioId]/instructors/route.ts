import { NextRequest, NextResponse } from 'next/server';
import { InstructorService } from '@/lib/services/instructor.service';
import { CreateInstructorSchema } from '@/lib/schemas/instructor';
import { requirePartnerAuth } from '@/lib/auth/partner.server';
import { ZodError } from 'zod';

// Service 인스턴스 생성
const instructorService = new InstructorService();

/**
 * 스튜디오에 새 강사를 등록합니다
 * 
 * @description 파트너가 소유한 스튜디오에 강사를 등록합니다. 권한 검증 후 등록됩니다.
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.studioId - 스튜디오 ID
 * @param {Object} request.body - 강사 등록 정보
 * @param {string} request.body.name - 강사 이름 (최대 30자, 필수)
 * @param {'male'|'female'} request.body.gender - 성별 (필수)
 * @param {string} [request.body.contact] - 연락처 (선택사항)
 * @param {string} request.body.description - 강사 소개 (최대 300자, 필수)
 * @param {Object} [request.body.links] - 링크 정보
 * @param {string} [request.body.links.website] - 웹사이트 URL
 * @param {string} [request.body.links.sns] - SNS URL
 * @param {Array<Object>} [request.body.profileImages] - 프로필 이미지 (최대 2장)
 * @param {string} request.body.profileImages[].url - 이미지 URL
 * @param {string} [request.body.profileImages[].alt] - 이미지 대체 텍스트
 * @param {number} request.body.profileImages[].order - 이미지 순서
 * @param {number} request.body.experienceTotalYears - 총 경력 (1-20년, 필수)
 * @param {Array<Object>} request.body.specialties - 전문 분야 (최소 1개 이상, 필수)
 * @param {'요가'|'필라테스'|'헬스'|'수영'|'복싱'|'러닝'|'댄스'|'클라이밍'|'근력/체중운동'} request.body.specialties[].type - 전문 분야 종류
 * @param {number} request.body.specialties[].years - 해당 분야 경력
 * @param {Array<Object>} [request.body.certificates] - 자격증 정보
 * @param {string} request.body.certificates[].name - 자격증명
 * @param {string} request.body.certificates[].issuing_organization - 발급 기관
 * @param {string} request.body.certificates[].issue_date - 발급일 (YYYY-MM-DD)
 * @param {string} [request.body.certificates[].expiry_date] - 만료일 (YYYY-MM-DD)
 * @param {string} [request.body.certificates[].certificate_number] - 자격증 번호
 * @returns {Promise<NextResponse>} 201: 등록된 강사 정보, 400: 유효성 검증 실패, 403: 권한 없음, 500: 서버 오류
 * @example
 * // POST /api/partner/studios/uuid/instructors
 * {
 *   "name": "김요가",
 *   "gender": "female",
 *   "description": "10년 경력의 전문 요가 강사입니다",
 *   "experienceTotalYears": 10,
 *   "specialties": [{"type": "요가", "years": 10}]
 * }
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ studioId: string }> }
) {
  try {
    // 파트너 인증
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) return errorResponse;

    const { studioId } = await params;
    const body = await request.json();

    // studioId를 body에 추가
    const requestData = {
      ...body,
      studioId,
    };

    // 입력값 검증
    const validatedData = CreateInstructorSchema.parse(requestData);

    // 강사 생성 (인증된 파트너 ID와 함께)
    const instructor = await instructorService.createInstructor(
      validatedData,
      partner!.id
    );

    return NextResponse.json(instructor, { status: 201 });
  } catch (error) {
    console.error('Error creating instructor:', error);

    if (error instanceof ZodError) {
      return NextResponse.json(
        {
          error: '입력값이 올바르지 않습니다',
          details: error.issues,
        },
        { status: 400 }
      );
    }

    // 권한 에러 처리
    if (error instanceof Error && error.message.includes('권한')) {
      return NextResponse.json(
        { error: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: '강사 등록에 실패했습니다' },
      { status: 500 }
    );
  }
}

/**
 * 스튜디오의 강사 목록을 조회합니다
 * 
 * @description 파트너가 소유한 특정 스튜디오의 활성 상태 강사 목록을 조회합니다
 * @param {NextRequest} request - HTTP 요청 객체
 * @param {Object} params - 라우트 파라미터
 * @param {string} params.studioId - 스튜디오 ID
 * @returns {Promise<NextResponse>} 200: 강사 목록 배열, 403: 권한 없음, 500: 서버 오류
 * @example
 * // GET /api/partner/studios/uuid/instructors
 * // Response:
 * [
 *   {
 *     "id": "uuid",
 *     "studioId": "uuid",
 *     "name": "김요가",
 *     "gender": "female",
 *     "specialties": [{"type": "요가", "years": 10}],
 *     "experienceTotalYears": 10,
 *     "profileImages": [...],
 *     "status": "active",
 *     "createdAt": "2024-01-01T00:00:00Z"
 *   }
 * ]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ studioId: string }> }
) {
  try {
    // 파트너 인증
    const { partner, errorResponse } = await requirePartnerAuth(request);
    if (errorResponse) return errorResponse;

    const { studioId } = await params;

    // 스튜디오별 강사 목록 조회 (인증된 파트너 ID와 함께)
    const instructors = await instructorService.findInstructorsByStudio(
      studioId,
      partner!.id
    );
    
    return NextResponse.json(instructors);
  } catch (error) {
    console.error('Error fetching instructors:', error);

    // 권한 에러 처리
    if (error instanceof Error && error.message.includes('권한')) {
      return NextResponse.json(
        { error: error.message },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: '강사 목록 조회에 실패했습니다' },
      { status: 500 }
    );
  }
}