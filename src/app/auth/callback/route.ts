import { createServerClient } from '@supabase/ssr';
import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get('code');
  const redirectTo = requestUrl.searchParams.get('redirect_to') || '/';

  if (code) {
    const cookieStore = await cookies();
    
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return cookieStore.getAll();
          },
          setAll(cookiesToSet) {
            try {
              cookiesToSet.forEach(({ name, value, options }) =>
                cookieStore.set(name, value, options)
              );
            } catch {
              // 서버 컴포넌트에서 쿠키 설정이 실패할 수 있음
              // 이는 정상적인 상황임
            }
          },
        },
      }
    );

    try {
      // 인증 코드를 사용하여 세션 생성
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        console.error('인증 코드 교환 오류:', error);
        return NextResponse.redirect(new URL('/instructor/login?error=auth_failed', requestUrl.origin));
      }

      if (data.user) {
        console.log('로그인 성공:', data.user.email);
        
        // 사용자 메타데이터에 강사 역할 추가
        if (redirectTo.includes('/instructor')) {
          await supabase.auth.updateUser({
            data: { 
              user_role: 'instructor',
              last_login: new Date().toISOString()
            }
          });
        }

        // 성공적으로 로그인 후 지정된 경로로 리다이렉트
        return NextResponse.redirect(new URL(redirectTo, requestUrl.origin));
      }
    } catch (error) {
      console.error('인증 처리 중 오류:', error);
      return NextResponse.redirect(new URL('/instructor/login?error=callback_failed', requestUrl.origin));
    }
  }

  // 코드가 없거나 처리 실패 시 로그인 페이지로 리다이렉트
  return NextResponse.redirect(new URL('/instructor/login?error=no_code', requestUrl.origin));
}