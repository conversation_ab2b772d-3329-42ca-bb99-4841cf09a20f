import { CreateInstructorSchema } from '@/lib/schemas/instructor';
import { CreateStudioSchema } from '@/lib/schemas/studio';
import z from 'zod';

export const studioFormSchema = CreateStudioSchema.omit({
  partnerId: true,
  images: true,
  amenities: true,
  stationDistance: true,
  nearestStation: true,
  operatingHours: true,
  latitude: true,
  longitude: true,
  status: true,
}).extend({
  images: z
    .array(z.instanceof(File))
    .max(10, '이미지는 최대 10개까지 업로드 가능합니다')
    .optional(),
  phone: z.string().min(1, '전화번호를 입력해주세요'),
  links: z.object({
    website: z.union([z.url(), z.literal('')]),
    sns: z.union([z.url(), z.literal('')]),
  }),
  parking: z
    .object({
      type: z.enum(['free', 'paid']),
      price: z.number().optional(),
      description: z.string().optional(),
    })
    .optional(),
  shower: z
    .object({
      available: z.boolean(),
      description: z.string().optional(),
    })
    .optional(),
  workoutClothes: z
    .object({
      type: z.enum(['free', 'paid']),
      description: z.string().optional(),
    })
    .optional(),
  locker: z
    .object({
      type: z.enum(['free', 'paid']),
      price: z.number().optional(),
      description: z.string().optional(),
    })
    .optional(),
});

export type StudioFormData = z.infer<typeof studioFormSchema>;

export const instructorFormSchema = CreateInstructorSchema.omit({
  profileImages: true,
  studioId: true, // Form doesn't handle studio selection - will be added during submission
  links: true,
}).extend({
  links: z.object({
    website: z.union([z.url(), z.literal('')]),
    sns: z.union([z.url(), z.literal('')]),
  }),
  profileImages: z
    .array(z.instanceof(File))
    .max(3, '이미지는 최대 3개까지 업로드 가능합니다')
    .optional(),
});

export type InstructorFormData = z.infer<typeof instructorFormSchema>;

// TODO: create class form schema
