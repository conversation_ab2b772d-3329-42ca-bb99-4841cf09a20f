import { CreateStudioRequest } from '@/lib/api/partner/studio.schema';
import { InstructorFormData, StudioFormData } from '../_schemas/form.schema';
import { geocodeAddress } from '@/lib/actions/geocoding';
import { partnerService } from '@/lib/api/partner/studio.api';
import { StudioImage } from '@/lib/schemas/studio';
import { CreateInstructorRequest } from '@/lib/api/partner/instructor.schema';
import { instructorApi } from '@/lib/api/partner/instructor.api';

export async function mapStudioFormDataToRequestData(
  studioFormData: StudioFormData
): Promise<Omit<CreateStudioRequest, 'partnerId'>> {
  const {
    address,
    name,
    phone,
    addressDetail,
    description,
    links,
    locker,
    // operatingHours,
    parking,
    postalCode,
    shower,
    workoutClothes,
    images,
    // latitude,
    // longitude,
    // nearestStation,
    // stationDistance,
  } = studioFormData;

  const amenities: CreateStudioRequest['amenities'] = {};

  if (parking) {
    amenities.parking = [parking];
  }

  if (shower) {
    amenities.shower = [shower];
  }

  if (locker) {
    amenities.locker = [locker];
  }

  if (workoutClothes) {
    amenities.workoutClothes = [workoutClothes];
  }

  const coordinates = await geocodeAddress(address);
  if (!coordinates) {
    throw new Error('Failed to get coordinates');
  }

  let uploadedImages: StudioImage[] | undefined = undefined;
  console.log('images', images);
  if (images && images.length > 0) {
    const studioId = crypto.randomUUID();
    const uploadPromises = images.map(file =>
      partnerService.uploadStudioImage({
        file,
        studioId,
        prefix: 'gallery',
      })
    );

    const uploadResults = await Promise.all(uploadPromises);

    console.log(
      'Upload results - paths:',
      uploadResults.map(result => result.path)
    );
    console.log(
      'Upload results - urls:',
      uploadResults.map(result => result.url)
    );

    uploadedImages = uploadResults.map(result => ({
      path: result.path,
      url: result.url,
    }));
  }

  const req: Omit<CreateStudioRequest, 'partnerId'> = {
    name,
    phone,
    address,
    status: 'pending',
    latitude: coordinates.latitude,
    longitude: coordinates.longitude,
    ...(addressDetail && { addressDetail }),
    ...(description && { description }),
    ...(postalCode && { postalCode }),
    ...(links && { links }),
    // ...(operatingHours && { operatingHours }),
    // ...(nearestStation && { nearestStation }),
    // ...(stationDistance && { stationDistance }),
    ...(Object.keys(amenities).length > 0 && { amenities }),
    ...(uploadedImages && { images: uploadedImages }),
  };

  return req;
}

export async function mapInstructorFormDataToRequestData(
  formData: InstructorFormData
): Promise<CreateInstructorRequest> {
  const {
    name,
    gender,
    contact,
    description,
    links,
    experienceTotalYears,
    specialties,
    certificates,
    profileImages,
  } = formData;

  let uploadedImages: { path: string; url: string }[] | undefined = undefined;
  console.log('instructor images', profileImages);
  if (profileImages && profileImages.length > 0) {
    const uploadPromises = profileImages.map(file =>
      instructorApi.uploadInstructorImage({ file })
    );

    const uploadResults = await Promise.all(uploadPromises);

    console.log(
      'Upload results - paths:',
      uploadResults.map(result => result.path)
    );
    console.log(
      'Upload results - urls:',
      uploadResults.map(result => result.url)
    );

    uploadedImages = uploadResults.map(result => ({
      path: result.path,
      url: result.url,
    }));
  }

  const req: CreateInstructorRequest = {
    name,
    gender,
    description,
    experienceTotalYears,
    specialties,
    ...(contact && { contact }),
    ...(links && { links }),
    ...(certificates && { certificates }),
    ...(uploadedImages && { profileImages: uploadedImages }),
  };

  return req;
}
