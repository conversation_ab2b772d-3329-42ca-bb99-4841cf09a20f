'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { type Control } from 'react-hook-form';
import type { ClassRegistrationForm } from '../page';
import { SPECIALTY_OPTIONS, DIFFICULTY_LEVEL_OPTIONS } from '@/lib/constants/class-enums';

interface SpecialtyLevelSelectorProps {
  control: Control<ClassRegistrationForm>;
}

export function SpecialtyLevelSelector({
  control,
}: SpecialtyLevelSelectorProps) {
  return (
    <div className='flex flex-col gap-4'>
      <FormField
        control={control}
        name='specialty'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              운동 분야{' '}
              <span className='text-primary font-bold'>*</span>
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='운동 분야' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {SPECIALTY_OPTIONS.map(option => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={control}
        name='level'
        render={({ field }) => (
          <FormItem>
            <FormLabel>
              운동 수준{' '}
              <span className='text-primary font-bold'>*</span>
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
            >
              <FormControl>
                <SelectTrigger className='w-full'>
                  <SelectValue placeholder='운동 수준' />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {DIFFICULTY_LEVEL_OPTIONS.map(option => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
