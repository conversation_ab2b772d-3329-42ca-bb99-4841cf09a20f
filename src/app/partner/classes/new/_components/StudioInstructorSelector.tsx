'use client';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useWatch, type Control } from 'react-hook-form';
import type { ClassRegistrationForm } from '../page';

interface Studio {
  id: string;
  name: string;
}

interface Instructor {
  id: string;
  name: string;
  centerId: string;
}

interface StudioInstructorSelectorProps {
  control: Control<ClassRegistrationForm>;
  studios: Studio[];
  instructors: Instructor[];
}

export function StudioInstructorSelector({
  control,
  studios,
  instructors,
}: StudioInstructorSelectorProps) {
  const selectedStudioId = useWatch({
    control,
    name: 'studioId',
  });

  const filteredInstructors = instructors.filter(
    instructor => instructor.centerId === selectedStudioId
  );

  return (
    <div className=''>
      <h2 className='mb-4 text-base font-semibold'>센터/강사</h2>
      <div className='flex flex-col gap-4'>
        <FormField
          control={control}
          name='studioId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                센터 <span className='text-primary font-bold'>*</span>
              </FormLabel>
              <Select
                defaultValue={field.value}
                onValueChange={value => {
                  field.onChange(value);
                }}
              >
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='센터 선택' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {studios.map(center => (
                    <SelectItem key={center.id} value={center.id}>
                      {center.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={control}
          name='instructorId'
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                강사 <span className='text-primary font-bold'>*</span>
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                value={field.value}
                disabled={!selectedStudioId}
              >
                <FormControl>
                  <SelectTrigger className='w-full'>
                    <SelectValue placeholder='강사 선택' />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {filteredInstructors.map(instructor => (
                    <SelectItem key={instructor.id} value={instructor.id}>
                      {instructor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
