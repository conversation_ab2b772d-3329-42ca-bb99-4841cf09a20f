'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  DAY_OF_WEEK_OPTIONS,
  TARGET_AUDIENCE_OPTIONS,
} from '@/lib/constants/class-enums';
import { Clock, X } from 'lucide-react';
import MultipleImageUpload from '@/app/partner/_components/MultipleImageUpload';
import { StudioInstructorSelector } from './_components/StudioInstructorSelector';
import { SpecialtyLevelSelector } from './_components/SpecialtyLevelSelector';

// Mock 데이터
const mockCenters = [
  { id: '1', name: '강남 피트니스 센터' },
  { id: '2', name: '홍대 요가 스튜디오' },
  { id: '3', name: '잠실 필라테스 센터' },
];

const mockInstructors = [
  { id: '1', name: '김민지', centerId: '1' },
  { id: '2', name: '박철수', centerId: '1' },
  { id: '3', name: '이영희', centerId: '2' },
  { id: '4', name: '최은영', centerId: '3' },
];

const capacityOptions = [
  { value: '5', label: '5명' },
  { value: '8', label: '8명' },
  { value: '10', label: '10명' },
  { value: '12', label: '12명' },
  { value: '15', label: '15명' },
  { value: '20', label: '20명' },
];

const weeklyFrequencyOptions = [
  { value: '1', label: '주 1회' },
  { value: '2', label: '주 2회' },
  { value: '3', label: '주 3회' },
];

const classDurationOptions = [
  { value: '50', label: '50분' },
  { value: '60', label: '60분' },
  { value: '90', label: '90분' },
];

const timeOptions = Array.from({ length: 48 }, (_, i) => {
  const hour = Math.floor(i / 2);
  const minute = (i % 2) * 30;
  const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  return { value: timeString, label: timeString };
});

// 폼 스키마 정의
const classRegistrationSchema = z.object({
  studioId: z.string().min(1, '센터를 선택해주세요'),
  instructorId: z.string().min(1, '강사를 선택해주세요'),
  title: z.string().min(1, '제목을 입력해주세요'),
  description: z.string().min(1, '상세 소개를 입력해주세요'),
  specialty: z.string().min(1, '운동 분야를 선택해주세요'),
  level: z.string().min(1, '운동 수준을 선택해주세요'),
  targetGender: z.string().min(1, '수강 대상을 선택해주세요'),
  capacity: z.string().min(1, '수업 정원을 선택해주세요'),
  weeklyFrequency: z.string().min(1, '주 운동 횟수를 선택해주세요'),
  pricePerSession: z.string().min(1, '회당 수강료를 입력해주세요'),
  duration: z.string().min(1, '수업 시간을 선택해주세요'),
});

export type ClassRegistrationForm = z.infer<typeof classRegistrationSchema>;

interface ScheduleTime {
  id: string;
  groupId: string;
  dayOfWeek: string;
  startTime: string;
  endTime: string;
}
const createEmptySchedules = (count: number) => {
  return Array.from({ length: count }, () => ({
    dayOfWeek: '',
    startTime: '',
    endTime: '',
  }));
};

export default function NewClassPage() {
  const [uploadedImages, setUploadedImages] = useState<File[]>([]);
  const [schedules, setSchedules] = useState<ScheduleTime[]>([]);
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [newSchedule, setNewSchedule] = useState<
    {
      dayOfWeek: string;
      startTime: string;
      endTime: string;
    }[]
  >([]);

  const form = useForm<ClassRegistrationForm>({
    resolver: zodResolver(classRegistrationSchema),
    defaultValues: {
      studioId: '',
      instructorId: '',
      title: '',
      description: '',
      specialty: '',
      level: '',
      targetGender: '',
      capacity: '',
      weeklyFrequency: '',
      pricePerSession: '',
      duration: '',
    },
  });
  const weeklyFrequency = form.watch('weeklyFrequency');
  const selectedStudioId = form.watch('studioId');
  const duration = form.watch('duration');
  const filteredInstructors = mockInstructors.filter(
    instructor => instructor.centerId === selectedStudioId
  );

  const calculateEndTime = (
    startTime: string,
    durationMinutes: number
  ): string => {
    const [hours, minutes] = startTime.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes + durationMinutes;
    const endHours = Math.floor(totalMinutes / 60);
    const endMinutes = totalMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`;
  };

  const addSchedule = () => {
    // 각 스케줄 검증
    for (let i = 0; i < newSchedule.length; i++) {
      const schedule = newSchedule[i];
      if (!schedule.dayOfWeek || !schedule.startTime || !schedule.endTime) {
        alert(`${i + 1}번째 수업 시간의 모든 정보를 입력해주세요.`);
        return;
      }

      // 시작 시간이 종료 시간보다 늦은지 체크
      if (schedule.startTime >= schedule.endTime) {
        alert(`${i + 1}번째 수업의 시작 시간은 종료 시간보다 빨라야 합니다.`);
        return;
      }
    }

    // 새로 추가할 스케줄들 간의 시간 겹침 체크
    for (let i = 0; i < newSchedule.length; i++) {
      for (let j = i + 1; j < newSchedule.length; j++) {
        const schedule1 = newSchedule[i];
        const schedule2 = newSchedule[j];

        if (schedule1.dayOfWeek === schedule2.dayOfWeek) {
          if (
            schedule1.startTime < schedule2.endTime &&
            schedule1.endTime > schedule2.startTime
          ) {
            alert('같은 요일에 두 수업 시간이 겹칩니다.');
            return;
          }
        }
      }
    }

    const newSchedulesToAdd = newSchedule;

    // 기존 스케줄과의 충돌 체크
    for (const newSched of newSchedulesToAdd) {
      const timeConflict = schedules.some(existing => {
        if (existing.dayOfWeek !== newSched.dayOfWeek) return false;

        const newStart = newSched.startTime;
        const newEnd = newSched.endTime;
        const existingStart = existing.startTime;
        const existingEnd = existing.endTime;

        return newStart < existingEnd && newEnd > existingStart;
      });

      if (timeConflict) {
        alert(
          `${DAY_OF_WEEK_OPTIONS.find(d => d.value === newSched.dayOfWeek)?.label}에 시간이 겹치는 수업이 있습니다.`
        );
        return;
      }
    }

    // 선택된 주차 횟수에 따른 제한 체크
    const weeklyFrequency = parseInt(form.getValues('weeklyFrequency') || '0');
    if (
      weeklyFrequency > 0 &&
      schedules.length + newSchedulesToAdd.length > weeklyFrequency
    ) {
      alert(
        `주 ${weeklyFrequency}회로 설정되어 더 이상 시간을 추가할 수 없습니다.`
      );
      return;
    }

    // 스케줄 추가
    const groupId = `group-${Date.now()}`;
    const schedulesToAdd: ScheduleTime[] = newSchedulesToAdd.map(
      (sched, index) => ({
        id: `${Date.now()}-${index}`,
        groupId: groupId,
        dayOfWeek: sched.dayOfWeek,
        startTime: sched.startTime,
        endTime: sched.endTime,
      })
    );

    setSchedules(prev => [...prev, ...schedulesToAdd]);
    const currentWeeklyFrequency = parseInt(
      form.getValues('weeklyFrequency') || '2'
    );
    setNewSchedule(createEmptySchedules(currentWeeklyFrequency));
    setShowScheduleForm(false);
  };

  const removeSchedule = (id: string) => {
    setSchedules(prev => prev.filter(schedule => schedule.id !== id));
  };

  const removeScheduleGroup = (groupId: string) => {
    setSchedules(prev => prev.filter(schedule => schedule.groupId !== groupId));
  };

  // 스케줄을 그룹별로 묶기
  const groupedSchedules = schedules.reduce(
    (groups, schedule) => {
      const groupId = schedule.groupId;
      if (!groups[groupId]) {
        groups[groupId] = [];
      }
      groups[groupId].push(schedule);
      return groups;
    },
    {} as Record<string, ScheduleTime[]>
  );

  const onSubmit = (data: ClassRegistrationForm) => {
    // 이미지 업로드 검증
    if (uploadedImages.length === 0) {
      alert('최소 1개의 운동 대표 사진을 업로드해주세요.');
      return;
    }

    // 스케줄 검증
    if (schedules.length === 0) {
      alert('최소 1개의 수업 시간을 등록해주세요.');
      return;
    }

    // 주차 횟수와 스케줄 개수 일치 검증 (최소 2회)
    const weeklyFrequency = parseInt(data.weeklyFrequency);
    if (schedules.length < 2) {
      alert('최소 주 2회 수업 시간을 등록해주세요.');
      return;
    }
    if (schedules.length !== weeklyFrequency) {
      alert(
        `주 ${weeklyFrequency}회 설정에 맞춰 ${weeklyFrequency}개의 수업 시간을 등록해주세요. (현재 ${schedules.length}개)`
      );
      return;
    }

    console.log('제출된 데이터:', data);
    console.log('이미지:', uploadedImages);
    console.log('스케줄:', schedules);

    // TODO: API 호출로 클래스 등록
    alert('클래스가 성공적으로 등록되었습니다!');
  };

  useEffect(() => {
    if (weeklyFrequency) {
      setNewSchedule(createEmptySchedules(parseInt(weeklyFrequency)));
    }
  }, [weeklyFrequency]);

  useEffect(() => {
    if (duration) {
      const durationMinutes = parseInt(duration);

      // 기존 newSchedule의 종료 시간 재계산
      setNewSchedule(prev =>
        prev.map(schedule => {
          if (schedule.startTime) {
            return {
              ...schedule,
              endTime: calculateEndTime(schedule.startTime, durationMinutes),
            };
          }
          return schedule;
        })
      );
    }
  }, [duration]);

  return (
    <div className='p-3'>
      <div className='mb-4'>
        <h1 className='text-foreground text-lg font-semibold'>클래스 등록</h1>
      </div>

      <div className=''>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className='flex flex-col gap-6'
          >
            {/* 센터/강사 선택 */}
            <StudioInstructorSelector
              control={form.control}
              studios={mockCenters}
              instructors={mockInstructors}
            />

            {/* 기본 정보 */}
            <div className=''>
              <h2 className='mb-4 text-base font-semibold'>기본 정보</h2>
              <div className='flex flex-col gap-4'>
                {/* 제목 */}
                <FormField
                  control={form.control}
                  name='title'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 제목{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder='예: 초보자를 위한 하타 요가'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 상세 소개 */}
                <FormField
                  control={form.control}
                  name='description'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        그룹 운동 상세 소개{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='수업의 특징, 커리큘럼, 수강생에게 도움이 되는 점 등을 설명해주세요.'
                          className='min-h-[100px]'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* 운동 대표 사진 업로드 */}
                <MultipleImageUpload
                  images={uploadedImages}
                  onChange={setUploadedImages}
                  type='class'
                  maxImages={5}
                />

                <SpecialtyLevelSelector control={form.control} />

                <div className='flex flex-col gap-4'>
                  <FormField
                    control={form.control}
                    name='targetGender'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          수강 대상{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='수강 대상' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {TARGET_AUDIENCE_OPTIONS.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='capacity'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          수업 정원{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='수업 정원' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {capacityOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <label className='text-sm leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
                    수업 기간 <span className='text-primary font-bold'>*</span>
                  </label>
                  <Select disabled={true} defaultValue='1개월'>
                    <SelectTrigger className='w-full'>
                      <SelectValue placeholder='수업 기간' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='1개월'>1개월</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className='flex flex-col gap-4'>
                  <FormField
                    control={form.control}
                    name='weeklyFrequency'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          주 운동 횟수{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='주 운동 횟수' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {weeklyFrequencyOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='duration'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          수업 시간{' '}
                          <span className='text-primary font-bold'>*</span>
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='수업 시간' />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {classDurationOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='pricePerSession'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        회당 수강료{' '}
                        <span className='text-primary font-bold'>*</span>
                      </FormLabel>
                      <FormControl>
                        <div className='relative'>
                          <Input
                            type='number'
                            placeholder='20,000'
                            {...field}
                          />
                          <span className='text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm'>
                            원
                          </span>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            {/* 수업 시간 개설 */}
            <div className=''>
              <h2 className='mb-1 text-base font-semibold'>수업 시간 개설</h2>
              <div className='flex flex-col gap-1'>
                <p className='text-primary text-xs'>
                  1. 가능한 수업 시간을 모두 열어주세요.
                </p>
                <p className='text-primary text-xs'>
                  2. 수업 정원 모집이 완료되면 수업을 &apos;확정&apos;하고, 운동
                  시작일을 안내해주세요.
                </p>
              </div>
              <div className='my-2'>
                <Button
                  type='button'
                  variant='secondary'
                  onClick={() => {
                    if (!weeklyFrequency) {
                      alert('먼저 주 운동 횟수를 선택해주세요.');
                      return;
                    }

                    if (!duration) {
                      alert('먼저 수업 시간을 선택해주세요.');
                      return;
                    }

                    setShowScheduleForm(true);
                  }}
                  className='w-full cursor-pointer'
                >
                  수업 시간 추가
                </Button>
              </div>
              <div className='flex flex-col gap-4'>
                {/* schedules */}
                {schedules.length > 0 && (
                  <div className='flex flex-col gap-3'>
                    {Object.entries(groupedSchedules).map(
                      ([groupId, groupSchedules]) => (
                        <div
                          key={groupId}
                          className='relative rounded-sm border border-gray-200 bg-gray-50 p-3'
                        >
                          <div className='absolute top-2 right-2'>
                            <Button
                              type='button'
                              variant='ghost'
                              size='sm'
                              onClick={() => removeScheduleGroup(groupId)}
                              className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
                            >
                              <X size={14} />
                            </Button>
                          </div>
                          <div className='flex flex-col gap-1'>
                            {groupSchedules.map(schedule => (
                              <div
                                key={schedule.id}
                                className='flex items-center space-x-2'
                              >
                                <Clock
                                  size={14}
                                  className='text-muted-foreground'
                                />
                                <span className='text-sm text-gray-600'>
                                  {
                                    DAY_OF_WEEK_OPTIONS.find(
                                      d => d.value === schedule.dayOfWeek
                                    )?.label
                                  }{' '}
                                  {schedule.startTime} - {schedule.endTime}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )
                    )}
                  </div>
                )}

                {/* 수업 시간 추가 폼 */}
                {showScheduleForm && (
                  <section className='border-primary-2 bg-gray-50 p-2'>
                    <div className='flex flex-col gap-2'>
                      {newSchedule.map((schedule, index) => (
                        <div key={index} className='flex flex-col gap-2'>
                          <div>
                            <Select
                              value={schedule.dayOfWeek}
                              onValueChange={value =>
                                setNewSchedule(prev =>
                                  prev.map((item, i) =>
                                    i === index
                                      ? { ...item, dayOfWeek: value }
                                      : item
                                  )
                                )
                              }
                            >
                              <SelectTrigger className='w-full bg-white'>
                                <SelectValue placeholder='요일 선택' />
                              </SelectTrigger>
                              <SelectContent>
                                {DAY_OF_WEEK_OPTIONS.map(option => (
                                  <SelectItem
                                    key={option.value}
                                    value={option.value}
                                  >
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className='flex items-center gap-2'>
                            <div className='flex-1'>
                              <Select
                                value={schedule.startTime}
                                onValueChange={value => {
                                  setNewSchedule(prev =>
                                    prev.map((item, i) => {
                                      if (i === index) {
                                        const updatedItem = {
                                          ...item,
                                          startTime: value,
                                        };
                                        // 수업 시간이 설정되어 있으면 종료 시간 자동 계산
                                        if (duration && value) {
                                          const durationMinutes =
                                            parseInt(duration);
                                          updatedItem.endTime =
                                            calculateEndTime(
                                              value,
                                              durationMinutes
                                            );
                                        }
                                        return updatedItem;
                                      }
                                      return item;
                                    })
                                  );
                                }}
                              >
                                <SelectTrigger className='w-full bg-white'>
                                  <SelectValue placeholder='시작 시간' />
                                </SelectTrigger>
                                <SelectContent>
                                  {timeOptions.map(option => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className='flex-1'>
                              <Select
                                value={schedule.endTime}
                                onValueChange={value =>
                                  setNewSchedule(prev =>
                                    prev.map((item, i) =>
                                      i === index
                                        ? { ...item, endTime: value }
                                        : item
                                    )
                                  )
                                }
                                disabled={!!duration && !!schedule.startTime}
                              >
                                <SelectTrigger className='w-full bg-white'>
                                  <SelectValue
                                    placeholder={
                                      duration && schedule.startTime
                                        ? '자동 계산됨'
                                        : '종료 시간'
                                    }
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {timeOptions.map(option => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        </div>
                      ))}
                      <div className='flex space-x-2'>
                        <Button
                          type='button'
                          variant='outline'
                          size='sm'
                          onClick={() => {
                            setShowScheduleForm(false);
                            const currentWeeklyFrequency = parseInt(
                              form.getValues('weeklyFrequency') || '2'
                            );
                            setNewSchedule(
                              createEmptySchedules(currentWeeklyFrequency)
                            );
                          }}
                          className='flex-1'
                        >
                          취소
                        </Button>
                        <Button
                          type='button'
                          size='sm'
                          onClick={addSchedule}
                          className='flex-1'
                        >
                          시간 등록
                        </Button>
                      </div>
                    </div>
                  </section>
                )}

                {schedules.length === 0 && (
                  <div className='text-muted-foreground py-8 text-center'>
                    <Clock size={48} className='mx-auto mb-2 opacity-50' />
                    <p className='text-sm'>아직 등록된 수업 시간이 없습니다.</p>
                    <p className='text-xs'>
                      수업 시간 추가 버튼을 눌러 시간을 등록해주세요.
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* 수업 개설 버튼 */}
            <div className='pt-6'>
              <Button
                onClick={form.handleSubmit(onSubmit)}
                className='h-12 w-full text-base font-medium'
                size='lg'
              >
                수업 개설 등록
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}
