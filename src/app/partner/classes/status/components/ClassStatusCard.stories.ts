import type { Meta, StoryObj } from '@storybook/react-vite';
import ClassStatusCard from './ClassStatusCard';

const meta = {
  title: 'Partner/Classes/ClassStatusCard',
  component: ClassStatusCard,
  argTypes: {
    classInfo: {
      control: 'object',
      description: '클래스 정보 (제목, 정원)',
    },
    instructor: {
      control: 'object',
      description: '강사 정보',
    },
    schedule: {
      control: 'object',
      description: '수업 일정 배열',
    },
    enrollments: {
      control: 'object',
      description: '수강생 목록',
    },
  },
  args: {
    // 기본 action handlers는 필요에 따라 추가 가능
  },
} satisfies Meta<typeof ClassStatusCard>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * 기본 클래스 상태 카드
 * 수강생이 일부 등록된 일반적인 상태
 */
export const Default: Story = {
  args: {
    classInfo: {
      title: '하타 요가 기초',
      maxCapacity: 12,
    },
    instructor: {
      name: '김민지',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '10:00',
        endTime: '11:00',
      },
      {
        weekDay: '수',
        startTime: '10:00',
        endTime: '11:00',
      },
      {
        weekDay: '금',
        startTime: '10:00',
        endTime: '11:00',
      },
    ],
    enrollments: [
      { name: '박수진' },
      { name: '이영희' },
      { name: '최민수' },
      { name: '강지은' },
      { name: '윤서연' },
    ],
  },
};

/**
 * 정원 마감 클래스
 * 최대 수용 인원이 모두 찬 상태
 */
export const FullCapacity: Story = {
  args: {
    classInfo: {
      title: '크로스핏 초급',
      maxCapacity: 8,
    },
    instructor: {
      name: '박철수',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '19:00',
        endTime: '20:00',
      },
      {
        weekDay: '목',
        startTime: '19:00',
        endTime: '20:00',
      },
    ],
    enrollments: [
      { name: '김태현' },
      { name: '이소영' },
      { name: '박민호' },
      { name: '정수빈' },
      { name: '한지우' },
      { name: '송민경' },
      { name: '오준석' },
      { name: '임나영' },
    ],
  },
};

/**
 * 신청자 없는 클래스
 * 아직 수강생이 등록되지 않은 상태
 */
export const NoEnrollments: Story = {
  args: {
    classInfo: {
      title: '매트 필라테스',
      maxCapacity: 10,
    },
    instructor: {
      name: '최은영',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '14:00',
        endTime: '15:00',
      },
      {
        weekDay: '금',
        startTime: '14:00',
        endTime: '15:00',
      },
    ],
    enrollments: [],
  },
};

/**
 * 1:1 개인 수업
 * 정원이 1명인 개인 수업 클래스
 */
export const PersonalTraining: Story = {
  args: {
    classInfo: {
      title: '개인 맞춤 PT',
      maxCapacity: 1,
    },
    instructor: {
      name: '전문트레이너',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '16:00',
        endTime: '17:00',
      },
    ],
    enrollments: [{ name: '김현우' }],
  },
};

/**
 * 매일 수업
 * 주 7일 모두 진행되는 클래스
 */
export const DailyClass: Story = {
  args: {
    classInfo: {
      title: '모닝 요가',
      maxCapacity: 15,
    },
    instructor: {
      name: '이요가',
    },
    schedule: [
      { weekDay: '월', startTime: '07:00', endTime: '08:00' },
      { weekDay: '화', startTime: '07:00', endTime: '08:00' },
      { weekDay: '수', startTime: '07:00', endTime: '08:00' },
      { weekDay: '목', startTime: '07:00', endTime: '08:00' },
      { weekDay: '금', startTime: '07:00', endTime: '08:00' },
      { weekDay: '토', startTime: '08:00', endTime: '09:00' },
      { weekDay: '일', startTime: '08:00', endTime: '09:00' },
    ],
    enrollments: [{ name: '안수정' }, { name: '홍길동' }, { name: '김철민' }],
  },
};

/**
 * 저녁 시간대 클래스
 * 직장인들을 위한 퇴근 후 수업
 */
export const EveningClass: Story = {
  args: {
    classInfo: {
      title: '저녁 복싱 클래스',
      maxCapacity: 12,
    },
    instructor: {
      name: '최복싱',
    },
    schedule: [
      {
        weekDay: '월',
        startTime: '20:00',
        endTime: '21:30',
      },
      {
        weekDay: '수',
        startTime: '20:00',
        endTime: '21:30',
      },
    ],
    enrollments: [
      { name: '직장인A' },
      { name: '직장인B' },
      { name: '직장인C' },
      { name: '직장인D' },
      { name: '직장인E' },
      { name: '직장인F' },
      { name: '직장인G' },
    ],
  },
};

/**
 * 거의 마감된 클래스
 * 정원에서 1-2명 부족한 상태
 */
export const AlmostFull: Story = {
  args: {
    classInfo: {
      title: '수영 중급반',
      maxCapacity: 6,
    },
    instructor: {
      name: '물고기코치',
    },
    schedule: [
      {
        weekDay: '화',
        startTime: '18:00',
        endTime: '19:00',
      },
      {
        weekDay: '목',
        startTime: '18:00',
        endTime: '19:00',
      },
      {
        weekDay: '토',
        startTime: '10:00',
        endTime: '11:00',
      },
    ],
    enrollments: [
      { name: '수영선수' },
      { name: '물개' },
      { name: '돌고래' },
      { name: '바다거북' },
      { name: '상어' },
    ],
  },
};
