import { Button } from '@/components/ui/button';

interface ClassStatusCardProps {
  classInfo: {
    title: string;
    maxCapacity: number;
  };
  instructor: {
    name: string;
  };
  schedule: {
    startTime: string;
    endTime: string;
    weekDay: string;
  }[];
  enrollments: {
    name: string;
  }[];
}
export default function ClassStatusCard({
  classInfo,
  enrollments,
  schedule,
  instructor,
}: ClassStatusCardProps) {
  const currentEnrollmentCount = enrollments.length;
  return (
    <div className='rounded-md border p-2'>
      {/* 강사 이름 */}
      <div className='flex items-center justify-between'>
        <div>{instructor.name}</div>
        <div>{classInfo.maxCapacity}명 모집</div>
      </div>
      <div>{classInfo.title}</div>
      {/* 강의 일정 */}
      <div className='rounded-md border bg-gray-100 p-2'>
        {schedule.map(item => (
          <div key={item.weekDay} className='flex items-center justify-between'>
            <div>{item.weekDay}요일</div>
            <div>
              {item.startTime} - {item.endTime}
            </div>
          </div>
        ))}
      </div>
      {/* 수강생 목록 */}
      <div className='flex items-center gap-2'>
        <Button className='flex-1' variant='secondary'>
          신청자 정보 보기 ({currentEnrollmentCount} 명)
        </Button>
        <Button className='flex-1'>수업 확정</Button>
      </div>
    </div>
  );
}
