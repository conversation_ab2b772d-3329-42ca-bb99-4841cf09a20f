import {
  ClassStatusDisplayText,
  ProfileCardProps,
} from '@/types/partner-dashboard';

/**
 * 파트너 프로필 카드 컴포넌트
 * 파트너 정보, 클래스 상태 통계, 월간 KPI를 표시
 */
export default function ProfileCard({
  partnerName,
  totalClasses,
  monthlyClasses,
  classStats,
  // monthlyStats,
  // onStatusClick,
}: ProfileCardProps) {
  const displayName = partnerName || '파트너님';

  return (
    <div className='p-3'>
      {/* 프로필 헤더 */}
      <div className='mb-6 flex items-center'>
        <div className='mr-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-indigo-400 to-purple-500'>
          <span className='text-xl font-bold text-white'>
            {displayName.charAt(0)}
          </span>
        </div>
        <div>
          <h2 className='text-xl font-bold text-black'>{displayName}</h2>
          <div className='flex items-center gap-2'>
            <p>
              총 클래스{' '}
              <span className='text-primary font-bold'>{totalClasses}</span>개
            </p>
            <p>
              이번 달 수업{' '}
              <span className='text-primary font-bold'>{monthlyClasses}</span>회
            </p>
          </div>
        </div>
      </div>

      <div className='mb-4 grid grid-cols-2 gap-2'>
        <div className='rounded-md bg-blue-50 p-3 text-center'>
          <p className='text-xl font-bold text-blue-700'>
            {classStats.recruiting}
          </p>
          <p className='text-sm text-blue-600'>
            {ClassStatusDisplayText.recruiting}
          </p>
        </div>

        <div className='rounded-md bg-green-50 p-3 text-center'>
          <p className='text-xl font-bold text-green-700'>
            {classStats.ongoing}
          </p>
          <p className='text-sm text-green-600'>
            {ClassStatusDisplayText.ongoing}
          </p>
        </div>

        <div className='rounded-md bg-gray-50 p-3 text-center'>
          <p className='text-xl font-bold text-gray-700'>
            {classStats.completed}
          </p>
          <p className='text-sm text-gray-600'>
            {ClassStatusDisplayText.completed}
          </p>
        </div>

        <div className='rounded-md bg-yellow-50 p-3 text-center'>
          <p className='text-xl font-bold text-yellow-700'>
            {classStats.upcoming}
          </p>
          <p className='text-sm text-yellow-600'>
            {ClassStatusDisplayText.upcoming}
          </p>
        </div>
      </div>

      {/* <div className='mb-4 grid grid-cols-2 gap-2'>
        <div className='rounded-md border p-3 text-center'>
          <p className='text-xl font-bold'>{classStats.recruiting}</p>
          <p className='text-sm'>{ClassStatusDisplayText.recruiting}</p>
        </div>

        <div className='rounded-md border p-3 text-center'>
          <p className='text-xl font-bold'>{classStats.ongoing}</p>
          <p className='text-sm'>{ClassStatusDisplayText.ongoing}</p>
        </div>

        <div className='rounded-md border p-3 text-center'>
          <p className='text-xl font-bold'>{classStats.completed}</p>
          <p className='text-sm'>{ClassStatusDisplayText.completed}</p>
        </div>

        <div className='rounded-md border p-3 text-center'>
          <p className='text-xl font-bold'>{classStats.upcoming}</p>
          <p className='text-sm'>{ClassStatusDisplayText.upcoming}</p>
        </div>
      </div> */}

      {/* <div className='flex items-center justify-around rounded-md border py-2'>
        <div className='text-center'>
          <div className='mb-2 flex items-center justify-center'>
            <svg
              className='mr-1 h-5 w-5 text-blue-500'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'
              />
            </svg>
          </div>
          <p className='text-2xl font-bold text-gray-900'>
            {monthlyStats.totalClasses}
          </p>
          <p className='text-sm text-gray-500'>이번 달 수업</p>
        </div>

        <div className='text-center'>
          <div className='mb-2 flex items-center justify-center'>
            <svg
              className='mr-1 h-5 w-5 text-green-500'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z'
              />
            </svg>
          </div>
          <p className='text-2xl font-bold text-gray-900'>
            {monthlyStats.totalAttendance}
          </p>
          <p className='text-sm text-gray-500'>총 출석</p>
        </div>

        <div className='text-center'>
          <div className='mb-2 flex items-center justify-center'>
            <svg
              className='mr-1 h-5 w-5 text-yellow-500'
              fill='none'
              stroke='currentColor'
              viewBox='0 0 24 24'
            >
              <path
                strokeLinecap='round'
                strokeLinejoin='round'
                strokeWidth={2}
                d='M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z'
              />
            </svg>
          </div>
          <p className='text-2xl font-bold text-gray-900'>
            {monthlyStats.completionRate}%
          </p>
          <p className='text-sm text-gray-500'>완료율</p>
        </div>
      </div> */}
    </div>
  );
}
