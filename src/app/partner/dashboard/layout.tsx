import { Metadata } from 'next';

export const metadata: Metadata = {
  title: '파트너 대시보드 | ShallWe',
  description:
    '파트너 대시보드에서 클래스 현황, 예약 통계, 수업 일정을 한눈에 확인하세요.',
  keywords: ['파트너', '대시보드', '클래스 관리', '예약 현황', '수업 일정'],
  openGraph: {
    title: '파트너 대시보드 | ShallWe',
    description:
      '파트너 대시보드에서 클래스 현황, 예약 통계, 수업 일정을 한눈에 확인하세요.',
    type: 'website',
  },
};

interface PartnerDashboardLayoutProps {
  children: React.ReactNode;
}

/**
 * 파트너 대시보드 레이아웃
 * 메타데이터만 설정하고 네비게이션은 상위 파트너 레이아웃에서 처리
 */
export default function PartnerDashboardLayout({
  children,
}: PartnerDashboardLayoutProps) {
  return <div className=''>{children}</div>;
}
