'use client';

import { AppBar } from '@/components/layout/AppBar';
import {
  PartnerBottomNav,
  shouldShowPartnerBottomNav,
} from '@/app/partner/_components/PartnerBottomNav';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

interface PartnerLayoutProps {
  children: React.ReactNode;
}

export default function PartnerLayout({ children }: PartnerLayoutProps) {
  const pathname = usePathname();
  const showBottomNav = shouldShowPartnerBottomNav(pathname);

  return (
    <>
      <AppBar />
      <main className={cn('bg-white', showBottomNav ? 'pb-20' : undefined)}>
        {children}
      </main>
      {showBottomNav && <PartnerBottomNav />}
    </>
  );
}
