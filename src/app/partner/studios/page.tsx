'use client';
import { AddressSearch } from '@/components/shared/AddressSearch';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { partnerService } from '@/lib/api/partner/studio.api';
import { DevTool } from '@hookform/devtools';
import { zodResolver } from '@hookform/resolvers/zod';
import { Car, Droplets, Lock, Shirt } from 'lucide-react';
import { useState } from 'react';
import { Controller, SubmitHandler, useForm } from 'react-hook-form';
import { NumericFormat, PatternFormat } from 'react-number-format';
import { toast } from 'sonner';
import MultipleImageUpload from '../_components/MultipleImageUpload';
import { StudioFormData, studioFormSchema } from '../_schemas/form.schema';
import { mapStudioFormDataToRequestData } from '../_utils/transform';

// form schema is not same as request schema

// 시설 옵션들
const parkingAvailabilityOptions = [
  { value: 'available', label: '있음' },
  { value: 'none', label: '없음' },
];
// if parking is available, then show parkingTypeOptions

const parkingTypeOptions = [
  { value: 'free', label: '무료' },
  { value: 'paid', label: '유료' },
];

// if parking is paid, then show parkingPriceOptions

const showerOptions = [
  { value: 'none', label: '없음' },
  { value: 'available', label: '있음' },
];

const exerciseClothingOptions = [
  { value: 'free', label: '무료' },
  { value: 'paid', label: '유료' },
];

const lockerOptions = [
  { value: 'free', label: '무료' },
  { value: 'paid', label: '유료' },
];

// const personalExerciseOptions = [
//   { value: 'none', label: '없음' },
//   { value: 'available', label: '있음' },
// ];

const towelOptions = [
  { value: 'free', label: '무료' },
  { value: 'paid', label: '유료' },
];

// POST /api/partner/studios

// 시설 정보 아이템 컴포넌트
interface FacilityItemProps {
  icon: React.ReactNode;
  label: string;
  children: React.ReactNode;
}

const FacilityItem: React.FC<FacilityItemProps> = ({
  icon,
  label,
  children,
}) => (
  <div className='flex items-center gap-1'>
    <div className='flex min-w-[100px] flex-[0.4] items-center gap-1'>
      {icon}
      <span className='text-sm font-medium'>{label}</span>
    </div>
    <div className='flex-1'>{children}</div>
  </div>
);

export default function PartnerStudiosPage() {
  const {
    setValue,
    control,
    register,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<StudioFormData>({
    resolver: zodResolver(studioFormSchema),
    defaultValues: {
      address: '',
      name: '',
      description: '',
      phone: '',
      links: {
        website: '',
        sns: '',
      },
    },
  });

  const [parkingAvailability, setParkingAvailability] = useState<
    'available' | 'none' | undefined
  >(undefined);

  // Loading states (local for UI feedback)
  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const onSubmit: SubmitHandler<StudioFormData> = async data => {
    try {
      setIsLoading(true);
      setSubmitError(null);
      const requestData = await mapStudioFormDataToRequestData(data);
      await partnerService.createStudio(requestData);
      toast.success('센터가 성공적으로 등록되었습니다.');
    } catch (error) {
      console.error('센터 등록 실패:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const address = watch('address');
  return (
    <div className='p-3'>
      <div className='mb-4'>
        <h1 className='text-foreground text-lg font-semibold'>센터 관리</h1>
      </div>

      <div className=''>
        <form
          onSubmit={handleSubmit(onSubmit, error => {
            console.log('error', error);
            toast.error('스튜디오 생성에 실패했습니다.');
          })}
          className='flex flex-col gap-6'
        >
          {/* 폼 에러 알림 */}
          {/* 기본 정보 */}
          <div className=''>
            <h2 className='mb-4 text-base font-semibold'>기본 정보</h2>
            <div className='flex flex-col gap-5'>
              {/* 센터 주소 등록 */}
              <div>
                <label className='text-sm font-medium'>
                  센터 주소 등록
                  <span className='text-primary font-bold'>*</span>
                </label>
                <Controller
                  control={control}
                  name='address'
                  render={({ field }) => (
                    <AddressSearch
                      onComplete={data => {
                        field.onChange(data.address);
                      }}
                      placeholder='주소를 검색해주세요'
                      buttonText='주소 찾기'
                      value={field.value}
                    />
                  )}
                />
                {errors.address && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.address.message}
                  </p>
                )}
              </div>

              {/* 상세 주소 입력 (선택사항) */}
              {address && (
                <div>
                  <label className='text-sm font-medium'>
                    상세 주소 (선택)
                  </label>
                  <Input
                    {...register('addressDetail')}
                    placeholder='동, 호수 등 상세 주소를 입력해주세요'
                    className={errors.addressDetail ? 'border-destructive' : ''}
                  />
                  {errors.addressDetail && (
                    <p className='text-destructive mt-1 text-xs font-semibold'>
                      {errors.addressDetail.message}
                    </p>
                  )}
                </div>
              )}

              {/* 센터 이름 */}
              <div>
                <label className='text-sm font-medium'>
                  센터 이름
                  <span className='text-primary font-bold'>*</span>
                </label>
                <Input
                  placeholder='예: 헬씨 피트니스 센터'
                  {...register('name')}
                  className={errors.name ? 'border-destructive' : ''}
                />
                {errors.name && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.name.message}
                  </p>
                )}
              </div>

              <div>
                <label className='text-sm font-medium'>
                  센터 소개
                  <span className='text-primary font-bold'>*</span>
                </label>
                <Textarea
                  placeholder='센터의 특징과 장점을 소개해주세요.'
                  className={`min-h-[100px] ${errors.description ? 'border-destructive' : ''}`}
                  {...register('description')}
                />
                {errors.description && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.description.message}
                  </p>
                )}
              </div>

              {/* 센터 대표사진 업로드 */}
              <Controller
                control={control}
                name='images'
                render={({ field }) => (
                  <MultipleImageUpload
                    type='studio'
                    images={field.value || []}
                    onChange={field.onChange}
                  />
                )}
              />
              {errors.images && (
                <p className='text-destructive mt-1 text-xs font-semibold'>
                  {errors.images.message}
                </p>
              )}

              <div>
                <label className='text-sm font-medium'>
                  센터 대표 연락처
                  <span className='text-primary font-bold'>*</span>
                </label>

                <Controller
                  control={control}
                  name='phone'
                  render={({ field }) => (
                    // TODO: Add hyphen to phone number
                    <PatternFormat
                      format='###-####-####'
                      mask='_'
                      allowEmptyFormatting={false}
                      customInput={Input}
                      placeholder='010-1234-5678'
                      value={field.value}
                      onValueChange={values => {
                        field.onChange(values.value || '');
                      }}
                    />
                  )}
                />
                {errors.phone && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.phone.message}
                  </p>
                )}
              </div>

              <div>
                <label className='text-sm font-medium'>
                  센터 소개 링크 (선택)
                </label>
                <Input
                  {...register('links.website')}
                  placeholder='https://example.com'
                />
                {errors.links?.website && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.links.website.message}
                  </p>
                )}
              </div>

              <div>
                <label className='text-sm font-medium'>SNS 링크 (선택)</label>
                <Input
                  placeholder='https://instagram.com/yourpage'
                  {...register('links.sns')}
                />
                {errors.links?.sns && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.links.sns.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* register parking value is type and price, desc */}
          {/* 시설 정보 */}
          <div className=''>
            <h2 className='mb-4 text-base font-semibold'>시설 정보 (선택)</h2>
            <div className='flex flex-col gap-4'>
              {/* 주차장 */}
              <FacilityItem
                icon={<Car size={16} className='text-black' />}
                label='주차장'
              >
                <div className='flex gap-1'>
                  <div className='flex-1'>
                    <Select
                      value={parkingAvailability}
                      onValueChange={value => {
                        setParkingAvailability(value as 'available' | 'none');
                      }}
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='주차장 유무' />
                      </SelectTrigger>
                      <SelectContent>
                        {parkingAvailabilityOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  {/* If available, show parking type select */}
                  {parkingAvailability === 'available' && (
                    <div className='flex-1'>
                      <Controller
                        control={control}
                        name='parking'
                        render={({ field }) => (
                          <Select
                            value={field.value?.type || ''}
                            onValueChange={value => {
                              const newParkingItem = {
                                type: value as 'free' | 'paid',
                                price: value === 'paid' ? 10000 : 0,
                                description:
                                  value === 'paid'
                                    ? '유료 주차장 이용 가능'
                                    : '무료 주차장 이용 가능',
                              };
                              field.onChange(newParkingItem);
                            }}
                          >
                            <SelectTrigger className='w-full'>
                              <SelectValue placeholder='주차 요금' />
                            </SelectTrigger>
                            <SelectContent>
                              {parkingTypeOptions.map(option => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                    </div>
                  )}
                </div>
                {errors.parking && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.parking.message}
                  </p>
                )}
              </FacilityItem>

              {/* 샤워실 */}
              <FacilityItem
                icon={<Droplets size={16} className='text-black' />}
                label='샤워실'
              >
                <Controller
                  control={control}
                  name='shower'
                  render={({ field }) => (
                    <Select
                      value={
                        field.value?.available !== undefined
                          ? field.value.available
                            ? 'available'
                            : 'none'
                          : ''
                      }
                      onValueChange={value => {
                        const newShowerItem = {
                          available: value === 'available',
                          description:
                            value === 'available'
                              ? '샤워실 완비'
                              : '샤워실 없음',
                        };
                        field.onChange(newShowerItem);
                      }}
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='샤워실 선택' />
                      </SelectTrigger>
                      <SelectContent>
                        {showerOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.shower && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.shower.message}
                  </p>
                )}
              </FacilityItem>

              {/* 운동복 */}
              <FacilityItem
                icon={<Shirt size={16} className='text-black' />}
                label='운동복'
              >
                <Controller
                  control={control}
                  name='workoutClothes'
                  render={({ field }) => (
                    <Select
                      value={field.value?.type || ''}
                      onValueChange={value => {
                        const newWorkoutClothesItem = {
                          type: value as 'free' | 'paid',
                          description: `운동복 ${value === 'free' ? '무료' : '유료'} 제공`,
                        };
                        field.onChange(newWorkoutClothesItem);
                      }}
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='운동복 선택' />
                      </SelectTrigger>
                      <SelectContent>
                        {exerciseClothingOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.workoutClothes && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.workoutClothes.message}
                  </p>
                )}
              </FacilityItem>

              {/* 락커룸 */}
              <FacilityItem
                icon={<Lock size={16} className='text-black' />}
                label='락커룸'
              >
                <div className='flex gap-2'>
                  <div className='flex-1'>
                    <Controller
                      control={control}
                      name='locker'
                      render={({ field }) => (
                        <Select
                          value={field.value?.type || ''}
                          onValueChange={value => {
                            const newLockerItem = {
                              type: value as 'free' | 'paid',
                              price: value === 'paid' ? 10000 : 0,
                              description:
                                value === 'free'
                                  ? '무료 라커 이용 가능'
                                  : `유료 라커 (${value === 'paid' ? '10000' : '0'}원/월)`,
                            };
                            field.onChange(newLockerItem);
                          }}
                        >
                          <SelectTrigger className='w-full'>
                            <SelectValue placeholder='락커룸 선택' />
                          </SelectTrigger>
                          <SelectContent>
                            {lockerOptions.map(option => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                  {watch('locker')?.[0]?.type === 'paid' && (
                    <div className='flex-1'>
                      <div className='relative'>
                        <Controller
                          control={control}
                          name='locker'
                          render={({ field }) => (
                            <NumericFormat
                              thousandSeparator=','
                              allowNegative={false}
                              customInput={Input}
                              placeholder='10,000'
                              value={field.value?.price || 0}
                              onValueChange={values => {
                                const price = parseInt(values.value || '0');
                                field.onChange(price);

                                setValue('locker', {
                                  type: 'paid',
                                  price: price,
                                  description: `유료 라커 (${price}원/월)`,
                                });
                              }}
                            />
                          )}
                        />
                        <span className='text-muted-foreground absolute top-1/2 right-3 -translate-y-1/2 text-sm'>
                          원/월
                        </span>
                      </div>
                    </div>
                  )}
                </div>
                {errors.locker && (
                  <p className='text-destructive mt-1 text-xs font-semibold'>
                    {errors.locker.message}
                  </p>
                )}
              </FacilityItem>

              {/* 개인 추가 운동 */}
              {/* <FacilityItem
                icon={<Dumbbell size={16} className='text-black' />}
                label='개인 추가 운동'
              >
                <Controller
                  control={control}
                  name='amenities.others'
                  render={({ field }) => (
                    <Select
                      value={
                        field.value?.find(
                          item => item.name === '개인 추가 운동'
                        )
                          ? 'available'
                          : 'none'
                      }
                      onValueChange={value => {
                        const currentOthers = field.value || [];

                        // Remove existing personal exercise entry
                        const filteredOthers = currentOthers.filter(
                          item => item.name !== '개인 추가 운동'
                        );

                        // Add new entry if available
                        if (value === 'available') {
                          filteredOthers.push({
                            name: '개인 추가 운동',
                            description: '개인 추가 운동 가능',
                          });
                        }

                        field.onChange(filteredOthers);
                      }}
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='개인 추가 운동 선택' />
                      </SelectTrigger>
                      <SelectContent>
                        {personalExerciseOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </FacilityItem> */}

              {/* 운동 타월 */}
              {/* TODO: check personal or workout */}
              {/* <FacilityItem
                icon={<Hand size={16} className='text-black' />}
                label='운동 타월'
              >
                <Controller
                  control={control}
                  name='amenities.towel'
                  render={({ field }) => (
                    <Select
                      value={
                        field.value?.description?.includes('무료')
                          ? 'free'
                          : field.value?.description?.includes('유료')
                            ? 'paid'
                            : ''
                      }
                      onValueChange={value => {
                        field.onChange({
                          type: 'workout', // Schema expects 'personal' or 'workout'
                          available: true,
                          description: `운동 타월 ${value === 'free' ? '무료' : '유료'} 제공`,
                        });
                      }}
                    >
                      <SelectTrigger className='w-full'>
                        <SelectValue placeholder='운동 타월 선택' />
                      </SelectTrigger>
                      <SelectContent>
                        {towelOptions.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </FacilityItem> */}

              {/* 커스텀 시설 정보 목록 */}
              {/* {customFacilities.map((facility, index) => (
                <FacilityItem
                  key={index}
                  icon={<Plus size={16} className='text-black' />}
                  label={facility.name}
                >
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-gray-600'>
                      {facility.description}
                    </span>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => {
                        const currentAmenities = { ...amenities };
                        if (currentAmenities.others) {
                          currentAmenities.others =
                            currentAmenities.others.filter(
                              (_, i) => i !== index
                            );
                          setFormValue('amenities', currentAmenities);
                        }
                      }}
                      className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
                    >
                      <X size={14} />
                    </Button>
                  </div>
                </FacilityItem>
              ))} */}

              {/* 시설 정보 추가 버튼 */}
              {/* <div className=''>
                <Button
                  type='button'
                  variant='outline'
                  onClick={() => setShowCustomFacilityForm(true)}
                  className='w-full'
                >
                  <Plus size={16} className='mr-2' />
                  시설 정보 추가
                </Button>
              </div> */}

              {/* 커스텀 시설 정보 추가 폼 */}
              {/* {showCustomFacilityForm && (
                <div className='border-primary-2 rounded-sm bg-gray-50 p-3'>
                  <div className='flex flex-col gap-3'>
                    <div>
                      <label className='text-sm font-medium'>시설명</label>
                      <Input
                        value={newCustomKey}
                        onChange={e => setNewCustomKey(e.target.value)}
                        placeholder='예: 사우나'
                        className='bg-white'
                      />
                    </div>
                    <div>
                      <label className='text-sm font-medium'>시설 정보</label>
                      <Input
                        value={newCustomValue}
                        onChange={e => setNewCustomValue(e.target.value)}
                        placeholder='예: 무료 이용 가능'
                        className='bg-white'
                      />
                    </div>
                    <div className='flex space-x-2'>
                      <Button
                        type='button'
                        variant='outline'
                        size='sm'
                        onClick={() => {
                          setShowCustomFacilityForm(false);
                          setNewCustomKey('');
                          setNewCustomValue('');
                        }}
                        className='flex-1'
                      >
                        취소
                      </Button>
                      <Button
                        type='button'
                        size='sm'
                        onClick={addCustomFacility}
                        className='flex-1'
                      >
                        추가
                      </Button>
                    </div>
                  </div>
                </div>
              )} */}
            </div>
          </div>

          {/* 운영시간 */}
          {/* <div className=''>
            <h2 className='mb-4 text-base font-semibold'>운영시간 (선택)</h2>
            <div className='flex flex-col gap-3'>
              {[
                { key: 'monday', label: '월요일' },
                { key: 'tuesday', label: '화요일' },
                { key: 'wednesday', label: '수요일' },
                { key: 'thursday', label: '목요일' },
                { key: 'friday', label: '금요일' },
                { key: 'saturday', label: '토요일' },
                { key: 'sunday', label: '일요일' },
              ].map(({ key, label }) => {
                const daySchedule =
                  operatingHours[key as keyof typeof operatingHours];

                return (
                  <div key={key} className='flex items-center gap-2'>
                    <div className='flex min-w-[80px] flex-[0.3] items-center'>
                      <span className='text-sm font-medium'>{label}</span>
                    </div>

                    <div className='flex flex-1 items-center gap-2'>
                      <label className='flex items-center gap-2'>
                        <input
                          type='checkbox'
                          checked={daySchedule?.closed || false}
                          onChange={e => {
                            const currentHours = { ...operatingHours };
                            if (
                              !currentHours[key as keyof typeof operatingHours]
                            ) {
                              currentHours[key as keyof typeof operatingHours] =
                                {
                                  open: '09:00',
                                  close: '18:00',
                                  closed: false,
                                };
                            }
                            currentHours[
                              key as keyof typeof operatingHours
                            ].closed = e.target.checked;
                            setFormValue('operatingHours', currentHours);
                          }}
                          className='h-4 w-4'
                        />
                        <span className='text-sm text-gray-600'>휴무</span>
                      </label>

                      {!daySchedule?.closed && (
                        <>
                          <Input
                            type='time'
                            value={daySchedule?.open || '09:00'}
                            onChange={e => {
                              const currentHours = { ...operatingHours };
                              if (
                                !currentHours[
                                  key as keyof typeof operatingHours
                                ]
                              ) {
                                currentHours[
                                  key as keyof typeof operatingHours
                                ] = {
                                  open: '09:00',
                                  close: '18:00',
                                  closed: false,
                                };
                              }
                              currentHours[
                                key as keyof typeof operatingHours
                              ].open = e.target.value;
                              setFormValue('operatingHours', currentHours);
                            }}
                            className='w-24'
                          />
                          <span className='text-sm text-gray-400'>~</span>
                          <Input
                            type='time'
                            value={daySchedule?.close || '18:00'}
                            onChange={e => {
                              const currentHours = { ...operatingHours };
                              if (
                                !currentHours[
                                  key as keyof typeof operatingHours
                                ]
                              ) {
                                currentHours[
                                  key as keyof typeof operatingHours
                                ] = {
                                  open: '09:00',
                                  close: '18:00',
                                  closed: false,
                                };
                              }
                              currentHours[
                                key as keyof typeof operatingHours
                              ].close = e.target.value;
                              setFormValue('operatingHours', currentHours);
                            }}
                            className='w-24'
                          />
                        </>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div> */}

          {/* 센터 등록 버튼 */}
          <div className='pt-6'>
            <Button
              type='submit'
              className='h-12 w-full text-base font-medium'
              size='lg'
              disabled={isLoading}
            >
              센터등록
            </Button>
          </div>
        </form>
      </div>
      <DevTool control={control} placement='top-right' />
    </div>
  );
}
