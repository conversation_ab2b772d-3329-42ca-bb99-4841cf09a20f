'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import { getCurrentUser } from '@/lib/supabase/auth';
import { STORAGE_KEYS } from '@/lib/constants/onboarding';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

export default function OnboardingGuard({ children }: OnboardingGuardProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isChecking, setIsChecking] = useState(true);
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    const checkOnboardingStatus = async () => {
      // URL에 onboarding=true가 있으면 온보딩으로 강제 이동
      const forceOnboarding = searchParams.get('onboarding') === 'true';

      if (forceOnboarding && !pathname.startsWith('/onboarding')) {
        router.replace('/onboarding');
        return;
      }

      // 온보딩 관련 페이지나 파트너 페이지는 체크 제외
      const excludedPaths = [
        '/onboarding',
        '/onboarding/complete',
        '/partner',
        '/login',
        '/signup',
        '/auth',
      ];

      if (excludedPaths.some(path => pathname.startsWith(path))) {
        setShouldRender(true);
        setIsChecking(false);
        return;
      }

      try {
        // 1. 로그인 상태 확인 - 로그인된 사용자는 온보딩 스킵
        const user = await getCurrentUser();
        if (user) {
          setShouldRender(true);
          setIsChecking(false);
          return;
        }

        // 2. 로컬스토리지에서 온보딩 완료 상태 확인
        const isOnboardingCompleted = localStorage.getItem(
          STORAGE_KEYS.ONBOARDING_COMPLETED
        );

        if (!isOnboardingCompleted) {
          // 온보딩 미완료 시 온보딩 페이지로 리다이렉트
          router.replace('/onboarding');
          return;
        }

        setShouldRender(true);
      } catch (error) {
        console.error('온보딩 상태 확인 오류:', error);
        // 오류 시에도 렌더링 허용 (네트워크 오류 등)
        setShouldRender(true);
      } finally {
        setIsChecking(false);
      }
    };

    checkOnboardingStatus();
  }, [pathname, router, searchParams]);

  // 온보딩 완료 시 URL에서 onboarding 파라미터 제거
  useEffect(() => {
    if (pathname === '/onboarding/complete') {
      const currentParams = new URLSearchParams(searchParams.toString());
      if (currentParams.has('onboarding')) {
        currentParams.delete('onboarding');
        const newUrl = currentParams.toString()
          ? `${pathname}?${currentParams.toString()}`
          : pathname;
        router.replace(newUrl);
      }
    }
  }, [pathname, searchParams, router]);

  if (isChecking) {
    // TODO: Proper loading skeleton
    return (
      <div className='flex min-h-screen items-center justify-center bg-gray-50'>
        <div className='text-center'>
          <div className='mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-indigo-600 border-t-transparent'></div>
          <p className='text-sm text-gray-600'>로딩 중...</p>
        </div>
      </div>
    );
  }

  // 렌더링 허용된 경우에만 children 표시
  return shouldRender ? <>{children}</> : null;
}
