'use client';

import { Button } from '@/components/ui/button';
import { signInWithKakao } from '@/lib/supabase/auth';

interface KakaoButtonProps {
  redirectTo?: string;
  className?: string;
}

/**
 * 카카오 로그인 버튼 컴포넌트
 * 
 * @description 
 * 일반 사용자와 강사 로그인에서 공통으로 사용하는 카카오 로그인 버튼
 * Button 컴포넌트의 variant='kakao' 스타일 사용
 * 
 * @example
 * // 일반 사용자 로그인 (홈으로 이동)
 * <KakaoButton redirectTo="/" />
 * 
 * // 강사 로그인
 * <KakaoButton redirectTo="/instructor/dashboard" />
 */
export function KakaoButton({ 
  redirectTo = '/',
  className = '' 
}: KakaoButtonProps) {
  const handleKakaoLogin = async () => {
    try {
      await signInWithKakao(redirectTo);
    } catch (error) {
      console.error('카카오 로그인 오류:', error);
      alert('로그인에 실패했습니다. 다시 시도해주세요.');
    }
  };

  return (
    <Button
      onClick={handleKakaoLogin}
      variant='kakao'
      className={`h-14 w-full ${className}`}
    >
      <div className='flex items-center justify-center gap-2'>
        <div className='flex h-6 w-6 items-center justify-center rounded-full bg-black'></div>
        <span className='text-base font-bold'>
          카카오로 간편하게 시작하기
        </span>
      </div>
    </Button>
  );
}