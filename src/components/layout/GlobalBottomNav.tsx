'use client';

import { Button } from '@/components/ui/button';
import { useUserStore } from '@/contexts/user.store';
import { cn } from '@/lib/utils';
import { shouldHideBottomNav } from '@/lib/utils/navigation';
import {
  BarChart3,
  BookOpen,
  Building2,
  Calendar,
  Home,
  User,
} from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';

interface NavItem {
  label: string;
  icon: React.ReactNode;
  path: string;
  activePattern: string;
}

interface GlobalBottomNavProps {
  className?: string;
}

export function GlobalBottomNav({ className }: GlobalBottomNavProps) {
  const router = useRouter();
  const pathname = usePathname();
  // const { user, role } = useUser();
  const user = useUserStore(state => state.user);
  const role = useUserStore(state => state.role);
  const isAuthenticated = !!user;

  if (shouldHideBottomNav(pathname)) {
    return null;
  }

  const userNavItems: NavItem[] = [
    {
      label: '홈',
      icon: <Home className='h-5 w-5' />,
      path: '/',
      activePattern: '^/$',
    },
    // {
    //   label: '클래스',
    //   icon: <BookOpen className='h-5 w-5' />,
    //   path: '/classes',
    //   activePattern: '^/classes',
    // },
    {
      label: '예약내역',
      icon: <Calendar className='h-5 w-5' />,
      path: '/bookings',
      activePattern: '^/bookings',
    },
    {
      label: '프로필',
      icon: <User className='h-5 w-5' />,
      path: '/profile',
      activePattern: '^/profile',
    },
  ];

  const instructorNavItems: NavItem[] = [
    {
      label: '대시보드',
      icon: <BarChart3 className='h-5 w-5' />,
      path: '/instructor/dashboard',
      activePattern: '^/instructor/dashboard',
    },
    {
      label: '클래스',
      icon: <BookOpen className='h-5 w-5' />,
      path: '/instructor/classes',
      activePattern: '^/instructor/classes',
    },
    {
      label: '스튜디오',
      icon: <Building2 className='h-5 w-5' />,
      path: '/instructor/studios',
      activePattern: '^/instructor/studios',
    },
    {
      label: '프로필',
      icon: <User className='h-5 w-5' />,
      path: '/instructor/profile',
      activePattern: '^/instructor/profile',
    },
  ];

  const navItems = role === 'INSTRUCTOR' ? instructorNavItems : userNavItems;

  const isActive = (item: NavItem) => {
    const regex = new RegExp(item.activePattern);
    return regex.test(pathname);
  };

  const handleNavClick = (path: string) => {
    // 홈은 인증 없이 접근 가능
    if (path === '/') {
      router.push(path);
      return;
    }

    // 다른 페이지들은 인증 필요
    if (isAuthenticated === false) {
      router.push('/login');
      return;
    }

    router.push(path);
  };

  return (
    <nav
      className={cn(
        'fixed bottom-0 z-50 h-16 w-full max-w-[480px]',
        'border-t border-gray-200 bg-white',
        'right-0 left-0 mx-auto',
        className
      )}
    >
      <div className='flex h-full'>
        {navItems.map(item => {
          const active = isActive(item);
          return (
            <Button
              key={item.path}
              variant='ghost'
              size='sm'
              onClick={() => handleNavClick(item.path)}
              className={cn(
                'flex h-full flex-1 flex-col items-center justify-center gap-1 rounded-none',
                'text-xs font-medium transition-colors',
                active
                  ? 'text-primary bg-primary/10'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              )}
            >
              <div className={cn(active && 'text-primary')}>{item.icon}</div>
              <span className={cn(active && 'text-primary')}>{item.label}</span>
            </Button>
          );
        })}
      </div>
    </nav>
  );
}
