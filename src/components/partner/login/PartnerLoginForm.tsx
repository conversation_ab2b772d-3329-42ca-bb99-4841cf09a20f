'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { toast } from 'sonner';
import { But<PERSON> } from '@/components/ui/button';
import { EmailInput } from '@/components/partner/login/EmailInput';
import { PasswordInput } from '@/components/partner/login/PasswordInput';
import { RememberMeCheckbox } from '@/components/partner/login/RememberMeCheckbox';
import { StatusMessage } from '@/components/partner/login/StatusMessage';
import { PartnerLoginFormSchema } from '@/schemas/partner';
import { 
  PartnerLoginFormData, 
  PartnerLoginResponse,
  isPartnerLoginSuccess 
} from '@/types/partner';
import { loginPartner } from '@/lib/api/partner/auth';
import { cn } from '@/lib/utils';

interface PartnerLoginFormProps {
  className?: string;
  onSuccess?: (response: PartnerLoginResponse) => void;
}

export const PartnerLoginForm: React.FC<PartnerLoginFormProps> = ({
  className,
  onSuccess
}) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);
  const [partnerStatus, setPartnerStatus] = useState<'PENDING' | 'SUSPENDED' | 'REJECTED' | null>(null);

  const form = useForm<PartnerLoginFormData>({
    resolver: zodResolver(PartnerLoginFormSchema),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });

  const { watch, setValue, formState: { errors } } = form;
  const email = watch('email');
  const password = watch('password');
  const rememberMe = watch('rememberMe');

  const handleSubmit = async (data: PartnerLoginFormData) => {
    setIsLoading(true);
    setLoginError(null);
    setPartnerStatus(null);

    try {
      // 실제 API 호출
      const response = await loginPartner(data);
      
      if (isPartnerLoginSuccess(response)) {
        // 로그인 성공
        toast.success(response.message);
        
        // 파트너 상태에 따른 처리
        const { status } = response.data.partner;
        
        if (status === 'ACTIVE') {
          // 활성화된 파트너는 대시보드로 리다이렉트
          router.push(response.data.redirectUrl);
        } else {
          // PENDING, SUSPENDED, REJECTED 상태는 상태 메시지 표시
          setPartnerStatus(status as 'PENDING' | 'SUSPENDED' | 'REJECTED');
        }
        
        // onSuccess 콜백 호출 (있는 경우)
        if (onSuccess) {
          onSuccess(response);
        }
      } else {
        // 로그인 실패
        setLoginError(response.message);
      }
      
    } catch (error) {
      console.error('Login error:', error);
      setLoginError('서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.');
    } finally {
      setIsLoading(false);
    }
  };

  // 파트너 상태별 메시지 표시
  if (partnerStatus) {
    return (
      <div className={cn('w-full max-w-md mx-auto', className)}>
        <StatusMessage status={partnerStatus} />
        <Button
          onClick={() => {
            setPartnerStatus(null);
            form.reset();
          }}
          variant="outline"
          className="w-full mt-4"
        >
          다시 로그인
        </Button>
      </div>
    );
  }

  return (
    <div className={cn('w-full max-w-md mx-auto', className)}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* 이메일 입력 */}
        <EmailInput
          value={email}
          onChange={(value) => setValue('email', value)}
          error={errors.email?.message}
          disabled={isLoading}
          required
        />

        {/* 비밀번호 입력 */}
        <PasswordInput
          value={password}
          onChange={(value) => setValue('password', value)}
          error={errors.password?.message}
          disabled={isLoading}
          required
          showPassword={showPassword}
          onToggleShow={() => setShowPassword(!showPassword)}
        />

        {/* 로그인 유지 및 비밀번호 찾기 */}
        <div className="flex items-center justify-between">
          <RememberMeCheckbox
            checked={rememberMe || false}
            onChange={(checked) => setValue('rememberMe', checked)}
            disabled={isLoading}
          />
          
          <Link
            href="/partner/reset-password"
            className="text-sm text-purple-600 hover:text-purple-500 transition-colors"
          >
            비밀번호를 잊으셨나요?
          </Link>
        </div>

        {/* 에러 메시지 */}
        {loginError && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-red-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800">{loginError}</p>
              </div>
            </div>
          </div>
        )}

        {/* 로그인 버튼 */}
        <Button
          type="submit"
          disabled={isLoading}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                />
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              로그인 중...
            </div>
          ) : (
            '로그인'
          )}
        </Button>

        {/* 회원가입 링크 */}
        <div className="text-center">
          <span className="text-sm text-gray-600">
            아직 계정이 없으신가요?{' '}
            <Link
              href="/partner/register"
              className="font-medium text-purple-600 hover:text-purple-500 transition-colors"
            >
              파트너 가입하기
            </Link>
          </span>
        </div>
      </form>
    </div>
  );
};

export default PartnerLoginForm;