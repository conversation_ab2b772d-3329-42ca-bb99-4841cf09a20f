'use client';

import { create } from 'zustand';
import { combine, devtools } from 'zustand/middleware';
import { User } from '@supabase/supabase-js';
// import {
//   getCurrentUser,
//   getUserRole,
//   onAuthStateChange,
// } from '@/lib/supabase/auth';

interface UserState {
  user: User | null;
  role: string | null;
  loading: boolean;
}

const initialState: UserState = {
  user: null,
  role: null,
  loading: true,
};

export const useUserStore = create(
  devtools(
    combine(initialState, set => {
      const actions = {
        setUser: (user: User | null) => {
          set({ user }, false, 'setUser');
        },

        setRole: (role: string | null) => {
          set({ role }, false, 'setRole');
        },

        setLoading: (loading: boolean) => {
          set({ loading }, false, 'setLoading');
        },

        reset: () => {
          set(initialState, false, 'reset');
        },
      };

      return actions;
    }),
    {
      name: 'user-store', // DevTools에서 표시될 이름
      enabled: process.env.NODE_ENV === 'development', // 개발 환경에서만 활성화
    }
  )
);

// Development logging subscription
// if (process.env.NODE_ENV === 'development') {
//   let previousState = useUserStore.getState();

//   useUserStore.subscribe(state => {
//     // Log user changes
//     if (state.user !== previousState.user) {
//       console.log('👤 User updated:', state.user?.email || 'null');
//     }

//     // Log role changes
//     if (state.role !== previousState.role) {
//       console.log('🎭 Role updated:', state.role || 'null');
//     }

//     // Log loading state changes
//     if (state.loading !== previousState.loading) {
//       console.log('⏳ Loading state:', state.loading);
//     }

//     // Log complete state for complex changes
//     if (state.user && state.role && !previousState.user) {
//       console.log('✅ User authenticated:', {
//         email: state.user.email,
//         role: state.role,
//       });
//     }

//     if (!state.user && previousState.user) {
//       console.log('🚪 User logged out');
//     }

//     previousState = state;
//   });

//   console.log('🚀 User store initialized with dev logging');
// }

export const userStoreActions = {
  setUser: useUserStore.getState().setUser,
  setRole: useUserStore.getState().setRole,
  setLoading: useUserStore.getState().setLoading,
  reset: useUserStore.getState().reset,
};
