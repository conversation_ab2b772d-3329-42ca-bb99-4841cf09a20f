import { httpClient, HttpClient } from '@/lib/http-client';
import {
  classApiSchema,
  type CreateClassRequest,
  type CreateClassResponse,
  type GetClassesRequest,
  type GetClassesResponse,
  type GetClassResponse,
  type UpdateClassRequest,
  type UpdateClassResponse,
  type DeleteClassResponse,
} from './class.schema';
import { uploadApiSchema } from './upload.schema';

export class PartnerClassApi {
  private httpClient: HttpClient;
  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  uploadClassImage = async ({ file }: { file: File }) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'class');

    const json = await this.httpClient.post('/api/partner/upload', formData);
    const result =
      uploadApiSchema['/api/partner/upload'].response.safeParse(json);

    if (!result.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  };

  getAllClasses = async (
    req: GetClassesRequest
  ): Promise<GetClassesResponse> => {
    const endpoint = '/api/partner/classes';
    try {
      const searchParams = new URLSearchParams();
      if (req.studioId) searchParams.append('studioId', req.studioId);
      searchParams.append('page', req.page.toString());
      searchParams.append('limit', req.limit.toString());

      const url = searchParams.toString()
        ? `${endpoint}?${searchParams.toString()}`
        : endpoint;

      const json = await this.httpClient.get(url);
      const result =
        classApiSchema['GET /api/partner/classes'].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getAllClasses error', error);
      throw error;
    }
  };

  createClass = async (
    req: CreateClassRequest
  ): Promise<CreateClassResponse> => {
    const endpoint = '/api/partner/classes';
    try {
      const json = await this.httpClient.post(endpoint, req);
      const result =
        classApiSchema['POST /api/partner/classes'].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('createClass error', error);
      throw error;
    }
  };

  getClassById = async (classId: string): Promise<GetClassResponse> => {
    const endpoint = `/api/partner/classes/${classId}`;
    try {
      const json = await this.httpClient.get(endpoint);
      const result =
        classApiSchema['GET /api/partner/classes/{id}'].response.safeParse(
          json
        );

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getClassById error', error);
      throw error;
    }
  };

  updateClass = async (
    classId: string,
    req: UpdateClassRequest
  ): Promise<UpdateClassResponse> => {
    const endpoint = `/api/partner/classes/${classId}`;
    try {
      const json = await this.httpClient.put(endpoint, req);
      const result =
        classApiSchema['PUT /api/partner/classes/{id}'].response.safeParse(
          json
        );

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('updateClass error', error);
      throw error;
    }
  };

  deleteClass = async (classId: string): Promise<DeleteClassResponse> => {
    const endpoint = `/api/partner/classes/${classId}`;
    try {
      const json = await this.httpClient.delete(endpoint);
      const result =
        classApiSchema['DELETE /api/partner/classes/{id}'].response.safeParse(
          json
        );

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('deleteClass error', error);
      throw error;
    }
  };
}

export const classApi = new PartnerClassApi(httpClient);
