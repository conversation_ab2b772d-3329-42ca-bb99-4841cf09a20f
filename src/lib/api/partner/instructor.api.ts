import { httpClient, HttpClient } from '@/lib/http-client';
import {
  instructorApiSchema,
  type CreateInstructorRequest,
  type CreateInstructorResponse,
  type GetInstructorsResponse,
  type GetInstructorResponse,
  type UpdateInstructorRequest,
  type UpdateInstructorResponse,
  type DeleteInstructorResponse,
} from './instructor.schema';
import { uploadApiSchema } from './upload.schema';

export class PartnerInstructorApi {
  private httpClient: HttpClient;
  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }
  uploadInstructorImage = async ({ file }: { file: File }) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'instructor');

    const json = await this.httpClient.post('/api/partner/upload', formData);
    const result =
      uploadApiSchema['/api/partner/upload'].response.safeParse(json);

    if (!result.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  };

  createInstructor = async (
    studioId: string,
    req: CreateInstructorRequest
  ): Promise<CreateInstructorResponse> => {
    const endpoint = `/api/partner/studios/${studioId}/instructors`;
    try {
      const json = await this.httpClient.post(endpoint, req);
      const result =
        instructorApiSchema[
          'POST /api/partner/studios/{studioId}/instructors'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('createInstructor error', error);
      throw error;
    }
  };

  getAllInstructors = async (
    studioId: string
  ): Promise<GetInstructorsResponse> => {
    const endpoint = `/api/partner/studios/${studioId}/instructors`;
    try {
      const json = await this.httpClient.get(endpoint);
      const result =
        instructorApiSchema[
          'GET /api/partner/studios/{studioId}/instructors'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getAllInstructors error', error);
      throw error;
    }
  };

  getInstructorById = async (
    studioId: string,
    instructorId: string
  ): Promise<GetInstructorResponse> => {
    const endpoint = `/api/partner/studios/${studioId}/instructors/${instructorId}`;
    try {
      const json = await this.httpClient.get(endpoint);
      const result =
        instructorApiSchema[
          'GET /api/partner/studios/{studioId}/instructors/{instructorId}'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('getInstructorById error', error);
      throw error;
    }
  };

  updateInstructor = async (
    studioId: string,
    instructorId: string,
    req: UpdateInstructorRequest
  ): Promise<UpdateInstructorResponse> => {
    const endpoint = `/api/partner/studios/${studioId}/instructors/${instructorId}`;
    try {
      const json = await this.httpClient.put(endpoint, req);
      const result =
        instructorApiSchema[
          'PUT /api/partner/studios/{studioId}/instructors/{instructorId}'
        ].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      console.error('updateInstructor error', error);
      throw error;
    }
  };

  disableInstructor = async (
    studioId: string,
    instructorId: string
  ): Promise<void> => {
    const endpoint = `/api/partner/studios/${studioId}/instructors/${instructorId}`;

    try {
      await this.httpClient.delete(endpoint);

      // const result =
      //   instructorApiSchema[
      //     'DELETE /api/partner/studios/{studioId}/instructors/{instructorId}'
      //   ].response.safeParse(json);

      // if (!result.success) {
      //   throw new Error(
      //     `Invalid response: ${JSON.stringify(result.error.issues)}`
      //   );
      // }

      // return result.data;
    } catch (error) {
      console.error('disableInstructor error', error);
      throw error;
    }
  };
}

export const instructorApi = new PartnerInstructorApi(httpClient);
