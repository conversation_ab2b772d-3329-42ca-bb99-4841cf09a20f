import {
  CreateInstructorSchema,
  UpdateInstructorSchema,
} from '@/lib/schemas/instructor';
import z from 'zod';

export const InstructorResponseSchema = CreateInstructorSchema.omit({
  studioId: true,
}).extend({
  id: z.string(),
  studioId: z.string(),
  partnerId: z.string(),
  status: z.enum(['active', 'inactive', 'deleted']).default('active'),
  createdAt: z.string(),
  updatedAt: z.string(),
  profileImages: z
    .array(
      z.object({
        url: z.string(),
        path: z.string(),
      })
    )
    .nullable(),
});

export const instructorApiSchema = {
  'POST /api/partner/studios/{studioId}/instructors': {
    request: CreateInstructorSchema.omit({ studioId: true }),
    response: InstructorResponseSchema,
  },
  'GET /api/partner/studios/{studioId}/instructors': {
    request: z.object({}),
    response: z.array(InstructorResponseSchema),
  },
  'GET /api/partner/studios/{studioId}/instructors/{instructorId}': {
    request: z.object({}),
    response: InstructorResponseSchema,
  },
  'PUT /api/partner/studios/{studioId}/instructors/{instructorId}': {
    request: UpdateInstructorSchema.omit({ studioId: true }),
    response: InstructorResponseSchema,
  },
  'DELETE /api/partner/studios/{studioId}/instructors/{instructorId}': {
    request: z.object({}),
    response: z.object({
      message: z.string(),
      instructorId: z.string(),
    }),
  },
};

export type InstructorApiSchema = typeof instructorApiSchema;

// Request/Response 타입 정의
export type CreateInstructorRequest = z.infer<
  InstructorApiSchema['POST /api/partner/studios/{studioId}/instructors']['request']
>;
export type CreateInstructorResponse = z.infer<
  InstructorApiSchema['POST /api/partner/studios/{studioId}/instructors']['response']
>;

export type GetInstructorsResponse = z.infer<
  InstructorApiSchema['GET /api/partner/studios/{studioId}/instructors']['response']
>;

export type GetInstructorResponse = z.infer<
  InstructorApiSchema['GET /api/partner/studios/{studioId}/instructors/{instructorId}']['response']
>;

export type UpdateInstructorRequest = z.infer<
  InstructorApiSchema['PUT /api/partner/studios/{studioId}/instructors/{instructorId}']['request']
>;
export type UpdateInstructorResponse = z.infer<
  InstructorApiSchema['PUT /api/partner/studios/{studioId}/instructors/{instructorId}']['response']
>;

export type DeleteInstructorResponse = z.infer<
  InstructorApiSchema['DELETE /api/partner/studios/{studioId}/instructors/{instructorId}']['response']
>;

export type InstructorResponse = z.infer<typeof InstructorResponseSchema>;
