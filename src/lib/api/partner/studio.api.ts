// service layer

import { httpClient, HttpClient } from '@/lib/http-client';
import {
  CreateStudioRequest,
  CreateStudioResponse,
  studioApiSchema,
} from './studio.schema';
import { StudioImageType } from '@/lib/schemas/studio';
import { uploadApiSchema } from './upload.schema';

export class PartnerStudioApi {
  private httpClient: HttpClient;
  constructor(httpClient: HttpClient) {
    this.httpClient = httpClient;
  }

  uploadStudioImage = async ({
    file,
    studioId,
    prefix,
  }: {
    file: File;
    studioId: string;
    prefix: StudioImageType;
  }) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'studio');
    formData.append('prefix', prefix);
    formData.append('studioId', studioId);

    const json = await this.httpClient.post('/api/partner/upload', formData);
    const result =
      uploadApiSchema['/api/partner/upload'].response.safeParse(json);

    if (!result.success) {
      throw new Error(
        `Invalid response: ${JSON.stringify(result.error.issues)}`
      );
    }

    return result.data;
  };

  createStudio = async (
    req: Omit<CreateStudioRequest, 'partnerId'>
  ): Promise<CreateStudioResponse> => {
    try {
      const json = await this.httpClient.post('/api/partner/studios', req);

      const result =
        studioApiSchema['POST /api/partner/studios'].response.safeParse(json);

      if (!result.success) {
        throw new Error(
          `Invalid response: ${JSON.stringify(result.error.issues)}`
        );
      }

      return result.data;
    } catch (error) {
      // throw handleError(error);
      console.error('createStudio error', error);
      throw error;
    }
  };

  // editStudio = () => {};
}
export const partnerService = new PartnerStudioApi(httpClient);
