import { CreateStudioSchema, StudioResponseSchema } from '@/lib/schemas/studio';

import z from 'zod';

export const studioApiSchema = {
  'POST /api/partner/studios': {
    request: CreateStudioSchema,
    response: StudioResponseSchema,
  },
};

export type StudioApiSchema = typeof studioApiSchema;
export type CreateStudioRequest = z.infer<
  StudioApiSchema['POST /api/partner/studios']['request']
>;
export type CreateStudioResponse = z.infer<
  StudioApiSchema['POST /api/partner/studios']['response']
>;
