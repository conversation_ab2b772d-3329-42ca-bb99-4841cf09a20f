import { relations } from 'drizzle-orm';
import {
  boolean,
  date,
  decimal,
  integer,
  json,
  jsonb,
  pgTable,
  text,
  time,
  timestamp,
  uuid,
  varchar,
} from 'drizzle-orm/pg-core';

// ===== DB Level enum 제거 - 코드 레벨에서만 관리 =====
// 모든 enum 값은 TEXT 타입으로 저장하고 코드에서 검증

// ===== 코드 레벨 ENUM 상수 (확장) =====

// 회원 관련
export const GenderText = {
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  OTHER: 'OTHER',
} as const;

export const MemberRoleText = {
  STUDENT: 'STUDENT',
  INSTRUCTOR: 'INSTRUCTOR',
  ADMIN: 'ADMIN',
} as const;

export const MemberStatusText = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  SUSPENDED: 'SUSPENDED',
} as const;

export const SpecialtyText = {
  YOGA: 'YOGA',
  PILATES: 'PILATES',
  FITNESS: 'FITNESS',
  CROSSFIT: 'CROSSFIT',
  SWIMMING: 'SWIMMING',
  BOXING: 'BOXING',
  DANCE: 'DANCE',
  RUNNING: 'RUNNING',
  CLIMBING: 'CLIMBING',
  MARTIAL_ARTS: 'MARTIAL_ARTS',
  MEDITATION: 'MEDITATION',
  STRETCHING: 'STRETCHING',
  BARRE: 'BARRE',
  SPINNING: 'SPINNING',
  ZUMBA: 'ZUMBA',
  KICKBOXING: 'KICKBOXING',
  THERAPEUTIC: 'THERAPEUTIC',
} as const;

// 클래스 관련
export const StudioTypeText = {
  FITNESS: 'fitness',
  YOGA: 'yoga',
  PILATES: 'pilates',
  DANCE: 'dance',
  MARTIAL_ARTS: 'martial_arts',
  WELLNESS: 'wellness',
} as const;

// 운동 분야
export const ClassCategoryText = {
  YOGA: 'yoga',
  PILATES: 'pilates',
  FITNESS: 'fitness',
  SWIMMING: 'swimming',
  BOXING: 'boxing',
  RUNNING: 'running',
  DANCE: 'dance',
  CLIMBING: 'climbing',
  STRENGTH: 'strength',
} as const;

// 운동 수준
export const ClassLevelText = {
  BEGINNER: 'beginner',
  INTERMEDIATE: 'intermediate',
  ADVANCED: 'advanced',
} as const;

// 수강 대상
export const ClassTargetText = {
  WOMEN_ONLY: 'women_only',
  MEN_ONLY: 'men_only',
  MIXED: 'mixed',
} as const;

// 클래스 상태
export const ClassStatusText = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  // TODO: Legacy API 마이그레이션 완료 후 제거 예정
  RECRUITING: 'recruiting', // Legacy API 호환성
  UPCOMING: 'upcoming', // Legacy API 호환성
  PUBLISHED: 'published', // Legacy API 호환성
  DRAFT: 'draft', // Legacy API 호환성
  ONGOING: 'ongoing', // Legacy API 호환성
  COMPLETED: 'completed', // Legacy API 호환성
  CANCELLED: 'cancelled', // Legacy API 호환성
} as const;


// 클래스 스케줄 상태
export const ClassScheduleStatusText = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  CANCELLED: 'cancelled',
} as const;

// 요일 (영어 약어 사용)
export const DayOfWeekCode = {
  SUN: 'sun',
  MON: 'mon',
  TUE: 'tue',
  WED: 'wed',
  THU: 'thu',
  FRI: 'fri',
  SAT: 'sat',
} as const;

// Legacy 요일 (deprecated)
export const DayOfWeekText = {
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY',
} as const;

// 파트너 관련
export const PartnerStatusText = {
  PENDING: 'PENDING',
  ACTIVE: 'ACTIVE',
  SUSPENDED: 'SUSPENDED',
  REJECTED: 'REJECTED',
} as const;

// TypeScript 타입 정의
export type Gender = (typeof GenderText)[keyof typeof GenderText];
export type MemberRole = (typeof MemberRoleText)[keyof typeof MemberRoleText];
export type MemberStatus =
  (typeof MemberStatusText)[keyof typeof MemberStatusText];
export type Specialty = (typeof SpecialtyText)[keyof typeof SpecialtyText];
export type StudioType = (typeof StudioTypeText)[keyof typeof StudioTypeText];
export type ClassCategory =
  (typeof ClassCategoryText)[keyof typeof ClassCategoryText];
export type ClassLevel = (typeof ClassLevelText)[keyof typeof ClassLevelText];
export type ClassTarget = (typeof ClassTargetText)[keyof typeof ClassTargetText];
export type ClassStatus =
  (typeof ClassStatusText)[keyof typeof ClassStatusText];
export type ClassScheduleStatus = (typeof ClassScheduleStatusText)[keyof typeof ClassScheduleStatusText];
export type DayOfWeekCode = (typeof DayOfWeekCode)[keyof typeof DayOfWeekCode];
export type DayOfWeek = (typeof DayOfWeekText)[keyof typeof DayOfWeekText];
export type PartnerStatus = (typeof PartnerStatusText)[keyof typeof PartnerStatusText];

// ===== 파트너 관련 테이블 =====

/**
 * 파트너 정보 테이블
 *
 * @description
 * - 스튜디오 운영자(파트너)의 기본 정보를 저장
 * - Supabase auth.users와 1:1 관계로 연결
 * - 간소화된 회원가입 정보 (연락처 정보만)
 * - 비즈니스 정보는 별도 테이블에서 관리 예정
 *
 * @relationships
 * - auth.users.id → partners.user_id (1:1)
 *
 * @business_rules
 * - user_id는 auth.users.id 참조 (UNIQUE 제약)
 * - status가 'ACTIVE'인 경우만 서비스 이용 가능
 * - contact_phone 형식: 010-0000-0000
 */
export const partners = pgTable('partners', {
  id: uuid('id').primaryKey().defaultRandom(),
  user_id: uuid('user_id').notNull().unique(), // auth.users.id 참조
  email: text('email').notNull().unique(), // 이메일 (Supabase 독립성을 위해 복제)
  contact_name: text('contact_name').notNull(),
  contact_phone: text('contact_phone').notNull(),
  status: text('status').notNull().default(PartnerStatusText.PENDING), // PartnerStatus enum
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type Partner = typeof partners.$inferSelect;

// ===== 회원 관련 테이블 =====

/**
 * 회원 정보 테이블
 *
 * @description
 * - Supabase auth.users와 1:1 관계로 연결
 * - 모든 회원 유형(학생, 강사, 관리자)의 기본 정보를 저장
 *
 * @relationships
 * - auth.users.id → members.id (1:1, PK로 직접 연겴)
 *
 * @business_rules
 * - id는 auth.users.id와 동일한 UUID 사용
 * - status가 'ACTIVE'인 경우만 서비스 사용 가능
 */
export const members = pgTable('members', {
  id: uuid('id').primaryKey(), // auth.users.id와 동일한 UUID
  nickname: text('nickname'),
  name: text('name'), // 온보딩 1단계: 실명
  phone: text('phone'), // 온보딩 1단계: 전화번호
  gender: text('gender'), // 온보딩 1단계: 성별 (Gender enum)
  birth_date: date('birth_date'),
  role: text('role').notNull().default(MemberRoleText.STUDENT), // MemberRole enum
  status: text('status').default(MemberStatusText.ACTIVE), // MemberStatus enum
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type Member = typeof members.$inferSelect;

/**
 * 회원 선호도 설정 테이블
 *
 * @description
 * - 학생 회원의 온보딩 과정에서 수집된 선호도 정보를 저장
 * - 레코드 존재 여부로 온보딩 완료 상태 판단
 * - 클래스 추천 시스템의 기반 데이터로 활용
 *
 * @relationships
 * - members.id → member_preferences.member_id (1:1)
 *
 * @business_rules
 * - member_id당 하나의 레코드만 존재 (UNIQUE 제약)
 * - fitness_goals, preferred_specialties: 복수 선택 가능 (JSONB 배열)
 * - preferred_stations: 지하철역 정보 객체 배열
 * - preferred_days, preferred_time_slots: 복수 선택 가능
 * - fitness_level: 단일 선택 (ClassLevel enum)
 */
export const member_preferences = pgTable('member_preferences', {
  id: uuid('id').primaryKey().defaultRandom(),
  member_id: uuid('member_id').notNull().unique(), // members.id 참조 (FK 제약 없음)

  // 운동 목표 (복수 선택)
  fitness_goals: jsonb('fitness_goals').notNull(), // ["기초체력", "근력강화", "체형교정"] 형태

  // 선호 지역 - 지하철역 정보 (복수 선택)
  preferred_stations: jsonb('preferred_stations').notNull(), // [{"id": "222", "name": "강남", "line": "2호선"}] 형태

  // 선호 운동 종목 (복수 선택)
  preferred_specialties: jsonb('preferred_specialties').notNull(), // ["YOGA", "PILATES", "FITNESS"] 형태

  // 선호 요일 (복수 선택)
  preferred_days: jsonb('preferred_days').notNull(), // ["MONDAY", "WEDNESDAY", "FRIDAY"] 형태

  // 선호 시간대 (복수 선택)
  preferred_time_slots: jsonb('preferred_time_slots').notNull(), // ["오전", "저녁"] 형태

  // 운동 수준 (단일 선택)
  fitness_level: text('fitness_level').notNull(), // ClassLevel enum ("beginner", "intermediate", "advanced", "all_levels")

  // 메타데이터
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type MemberPreference = typeof member_preferences.$inferSelect;

// ===== 강사 테이블 =====

/**
 * 강사 정보 테이블
 *
 * @description
 * - 독립적인 강사 정보 저장 (members 테이블과 연결 없음)
 * - 기본 정보, 경력, 자격증 정보를 JSON으로 관리
 * - 순서 유지가 중요한 데이터이므로 JSON 타입 사용
 *
 * @business_rules
 * - name: 최대 30자
 * - description: 최대 300자 (프론트엔드에서 검증)
 * - gender: 'male' | 'female'
 * - specialties: 최소 1개 이상 필수
 * - profileImages: 최대 2장
 * - status: 'active' | 'deleted' (소프트 삭제)
 */
export const instructors = pgTable('instructors', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // 소속 정보
  studioId: uuid('studio_id').notNull().references(() => studios.id),
  partnerId: uuid('partner_id').notNull().references(() => partners.id),
  
  // 기본 정보
  name: varchar('name', { length: 30 }).notNull(), // 강사 이름
  gender: varchar('gender', { length: 10 }).notNull(), // 성별 (male/female)
  contact: varchar('contact', { length: 100 }), // 연락처 (선택)
  description: text('description').notNull(), // 강사 소개 (최대 300자)
  
  // 링크 정보 (SNS, 블로그 등)
  links: json('links'), // {website?: string, sns?: string}
  
  // 이미지 정보 (최대 2장)
  profileImages: json('profile_images'), // [{path: string, url: string}]
  
  // 경력 정보
  experienceTotalYears: integer('experience_total_years').notNull(), // 총 경력 (1-20년)
  specialties: json('specialties').notNull(), // [{type: string, years: number}] 형태
  
  // 자격증 정보
  certificates: json('certificates'), // [{name, issuing_organization, issue_date, expiry_date?, certificate_number?}]
  
  // 상태 관리
  status: varchar('status', { length: 20 }).notNull().default('active'), // active, deleted
  
  // 메타데이터
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type Instructor = typeof instructors.$inferSelect;

// ===== 클래스 도메인 테이블 =====

/**
 * 스튜디오 정보 테이블
 *
 * @description
 * - 피트니스 센터, 요가 스튜디오, 필라테스 스튜디오 등 모든 운동 공간 정보
 * - 확장성을 고려하여 다양한 운동 카테고리 지원
 * - 지리적 위치 정보와 편의시설 정보 포함
 *
 * @relationships
 * - studios.id ← class_templates.studio_id (1:N)
 *
 * @business_rules
 * - studio_type은 코드 레벨에서 enum으로 검증
 * - is_active가 false인 경우 새로운 클래스 등록 불가
 * - amenities와 links는 JSON 형태로 유연하게 관리
 */
export const studios = pgTable('studios', {
  id: uuid('id').primaryKey().defaultRandom(),
  partnerId: uuid('partner_id').notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  description: text('description'),
  address: varchar('address', { length: 255 }).notNull(),
  addressDetail: varchar('address_detail', { length: 100 }),
  postalCode: varchar('postal_code', { length: 10 }),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  nearestStation: varchar('nearest_station', { length: 100 }),
  stationDistance: integer('station_distance'), // 역까지의 거리 (미터)
  amenities: jsonb('amenities'),
  links: json('links'), // 관련 링크 (웹사이트, SNS 등)
  type: varchar('type').notNull(), // StudioType enum
  operatingHours: jsonb('operating_hours'), // 운영시간
  images: jsonb('images'), // JSONB 이미지 메타데이터 배열 (type, path, url, alt_text, display_order 등)
  status: varchar('status', { length: 20 }).notNull().default('pending'), // active, inactive, pending, closed
  createdAt: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type Studio = typeof studios.$inferSelect;

/**
 * 클래스 테이블
 *
 * @description
 * - 파트너가 등록하는 클래스 정보
 * - 강사, 스튜디오, 기본 정보 및 스케줄 관리
 *
 * @relationships
 * - studios.id → classes.studio_id (N:1)
 * - instructors.id → classes.instructor_id (N:1)
 * - classes.id ← class_schedules.class_id (1:N)
 *
 * @business_rules
 * - title: 최대 30자
 * - description: 최대 500자
 * - max_participants: 2-10명
 * - price_per_session: 5,000-40,000원
 * - images: 최대 5장
 */
export const classes = pgTable('classes', {
  id: uuid('id').primaryKey().defaultRandom(),
  
  // 기본 정보
  partner_id: uuid('partner_id').notNull(), // 클래스를 등록한 파트너
  studio_id: uuid('studio_id').notNull(),
  instructor_id: uuid('instructor_id').notNull(),
  title: varchar('title', { length: 30 }).notNull(),
  description: text('description').notNull(),
  
  // 운동 정보
  category: text('category').notNull(), // ClassCategory
  level: text('level').notNull(), // ClassLevel
  target: text('target').notNull(), // ClassTarget
  
  // 수업 설정
  max_participants: integer('max_participants').notNull(),
  price_per_session: integer('price_per_session').notNull(),
  session_duration_minutes: integer('session_duration_minutes').notNull(), // 수업 시간(분)
  
  // 수업 기간
  duration_weeks: integer('duration_weeks').notNull(), // 4주(1개월) or 8주(2개월)
  sessions_per_week: integer('sessions_per_week').notNull(), // 주당 횟수
  
  // 이미지 (JSON 배열)
  images: json('images'), // [{url: string}] 형태
  
  // 상태
  status: text('status').notNull().default(ClassStatusText.ACTIVE),
  visible: boolean('visible').default(true),
  
  // 메타데이터
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
  deleted_at: timestamp('deleted_at', { withTimezone: true }),
});

export type Class = typeof classes.$inferSelect;

/**
 * 클래스 스케줄 그룹 테이블
 *
 * @description
 * - 클래스의 스케줄 그룹 정보
 * - 같은 시간대의 여러 요일을 하나의 그룹으로 관리
 *
 * @relationships
 * - classes.id → class_schedule_groups.class_id (N:1)
 * - class_schedule_groups.id ← class_schedules.schedule_group_id (1:N)
 *
 * @business_rules
 * - 하나의 클래스는 여러 스케줄 그룹을 가질 수 있음
 * - 각 그룹은 동일한 시간대의 서로 다른 요일들로 구성
 * - 상태는 그룹 단위로 관리 (pending/confirmed/cancelled)
 */
export const class_schedule_groups = pgTable('class_schedule_groups', {
  id: integer('id').primaryKey().generatedAlwaysAsIdentity(),
  class_id: uuid('class_id').notNull(),
  
  // 상태
  status: text('status').notNull().default(ClassScheduleStatusText.PENDING),
  
  // 메타데이터
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type ClassScheduleGroup = typeof class_schedule_groups.$inferSelect;

/**
 * 클래스 스케줄 테이블
 *
 * @description
 * - 스케줄 그룹 내의 개별 스케줄 정보
 * - 요일별 시간 정보
 *
 * @relationships
 * - class_schedule_groups.id → class_schedules.schedule_group_id (N:1)
 *
 * @business_rules
 * - day_of_week: 영어 약어 (sun, mon, tue, wed, thu, fri, sat)
 * - start_time, end_time: HH:MM 형식
 * - 같은 그룹 내 모든 스케줄은 동일한 start_time, end_time을 가져야 함
 */
export const class_schedules = pgTable('class_schedules', {
  id: integer('id').primaryKey().generatedAlwaysAsIdentity(),
  schedule_group_id: integer('schedule_group_id').notNull(),
  
  // 스케줄 정보
  day_of_week: text('day_of_week').notNull(), // sun, mon, tue, wed, thu, fri, sat
  start_time: time('start_time').notNull(),
  end_time: time('end_time').notNull(),
  
  // 메타데이터
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated Legacy class_schedules - 기존 API 호환성용
 */
export const legacy_class_schedules = pgTable('legacy_class_schedules', {
  id: uuid('id').primaryKey().defaultRandom(),
  schedule_group_id: uuid('schedule_group_id').notNull(),
  day_of_week: text('day_of_week').notNull(),
  start_time: time('start_time').notNull(),
  end_time: time('end_time').notNull(),
  max_participants: integer('max_participants'),
  is_active: boolean('is_active').default(true),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

export type NewClassSchedule = typeof class_schedules.$inferSelect;
export type LegacyClassSchedule = typeof legacy_class_schedules.$inferSelect;

// ===== LEGACY 테이블들 (TODO: 마이그레이션 후 제거 예정) =====

/**
 * @deprecated 새로운 classes 테이블로 마이그레이션 예정
 * 기존 API 호환성을 위해 임시 유지
 */
export const class_templates = pgTable('class_templates', {
  id: uuid('id').primaryKey().defaultRandom(),
  studio_id: uuid('studio_id').notNull(),
  instructor_id: uuid('instructor_id').notNull(),
  title: text('title').notNull(),
  description: text('description'),
  curriculum: jsonb('curriculum'),
  category: text('category').notNull(),
  specialty: text('specialty').notNull(),
  level: text('level').notNull(),
  duration_minutes: integer('duration_minutes').notNull(),
  price_per_session: decimal('price_per_session', { precision: 10, scale: 2 }).notNull(),
  max_capacity: integer('max_capacity').notNull(),
  recruitment_start_date: date('recruitment_start_date'),
  recruitment_end_date: date('recruitment_end_date'),
  class_start_date: date('class_start_date'),
  class_end_date: date('class_end_date'),
  status: text('status').notNull().default('draft'),
  is_active: boolean('is_active').default(true),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated 새로운 구조로 마이그레이션 예정
 */
export const legacy_class_schedule_groups = pgTable('legacy_class_schedule_groups', {
  id: uuid('id').primaryKey().defaultRandom(),
  class_template_id: uuid('class_template_id').notNull(),
  group_name: text('group_name').notNull(),
  group_description: text('group_description'),
  group_start_date: date('group_start_date'),
  group_end_date: date('group_end_date'),
  max_participants: integer('max_participants').notNull(),
  price_per_session: decimal('price_per_session', { precision: 10, scale: 2 }),
  sessions_per_week: integer('sessions_per_week').default(0),
  is_active: boolean('is_active').default(true),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated 새로운 구조로 마이그레이션 예정
 */
export const class_occurrences = pgTable('class_occurrences', {
  id: uuid('id').primaryKey().defaultRandom(),
  class_template_id: uuid('class_template_id').notNull(),
  schedule_group_id: uuid('schedule_group_id').notNull(),
  class_schedule_id: uuid('class_schedule_id').notNull(),
  occurrence_date: date('occurrence_date').notNull(),
  start_time: time('start_time').notNull(),
  end_time: time('end_time').notNull(),
  max_participants: integer('max_participants').notNull(),
  status: text('status').notNull().default('scheduled'),
  attendance_count: integer('attendance_count').default(0),
  confirmed_enrollments: integer('confirmed_enrollments').default(0),
  instructor_notes: text('instructor_notes'),
  cancellation_reason: text('cancellation_reason'),
  is_substitute_class: boolean('is_substitute_class').default(false),
  original_occurrence_id: uuid('original_occurrence_id'),
  booking_opens_at: timestamp('booking_opens_at', { withTimezone: true }),
  booking_closes_at: timestamp('booking_closes_at', { withTimezone: true }),
  cancellation_deadline: timestamp('cancellation_deadline', { withTimezone: true }),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated 새로운 구조로 마이그레이션 예정
 */
export const class_enrollments = pgTable('class_enrollments', {
  id: uuid('id').primaryKey().defaultRandom(),
  member_id: uuid('member_id').notNull(),
  class_template_id: uuid('class_template_id').notNull(),
  schedule_group_id: uuid('schedule_group_id').notNull(),
  enrollment_status: text('enrollment_status').notNull().default('pending'),
  enrolled_at: timestamp('enrolled_at', { withTimezone: true }).defaultNow(),
  payment_id: text('payment_id'),
  paid_amount: decimal('paid_amount', { precision: 10, scale: 2 }),
  refund_amount: decimal('refund_amount', { precision: 10, scale: 2 }),
  refunded_at: timestamp('refunded_at', { withTimezone: true }),
  refund_reason: text('refund_reason'),
  notes: text('notes'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated 새로운 구조로 마이그레이션 예정
 */
export const class_attendances = pgTable('class_attendances', {
  id: uuid('id').primaryKey().defaultRandom(),
  class_occurrence_id: uuid('class_occurrence_id').notNull(),
  member_id: uuid('member_id').notNull(),
  enrollment_id: uuid('enrollment_id'),
  attendance_status: text('attendance_status').notNull(),
  checked_in_at: timestamp('checked_in_at', { withTimezone: true }),
  checked_out_at: timestamp('checked_out_at', { withTimezone: true }),
  notes: text('notes'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated Legacy API 호환성을 위한 강사 전문분야 테이블
 */
export const instructor_specialties = pgTable('instructor_specialties', {
  id: uuid('id').primaryKey().defaultRandom(),
  instructor_id: uuid('instructor_id').notNull(),
  specialty: text('specialty').notNull(),
  experience_years: integer('experience_years').notNull(),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated Legacy API 호환성을 위한 강사 자격증 테이블
 */
export const instructor_certificates = pgTable('instructor_certificates', {
  id: uuid('id').primaryKey().defaultRandom(),
  instructor_id: uuid('instructor_id').notNull(),
  certificate_name: text('certificate_name').notNull(),
  issuing_organization: text('issuing_organization').notNull(),
  issue_date: date('issue_date').notNull(),
  expiry_date: date('expiry_date'),
  certificate_number: text('certificate_number'),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated Legacy API 호환성을 위한 강사 계좌 테이블
 */
export const instructor_accounts = pgTable('instructor_accounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  instructor_id: uuid('instructor_id').notNull(),
  bank_name: text('bank_name').notNull(),
  account_number: text('account_number').notNull(),
  account_holder: text('account_holder').notNull(),
  is_verified: boolean('is_verified').default(false),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
  updated_at: timestamp('updated_at', { withTimezone: true }).defaultNow(),
});

/**
 * @deprecated 새로운 구조로 마이그레이션 예정
 */
export const class_reviews = pgTable('class_reviews', {
  id: uuid('id').primaryKey().defaultRandom(),
  member_id: uuid('member_id').notNull(),
  class_template_id: uuid('class_template_id').notNull(),
  instructor_id: uuid('instructor_id').notNull(),
  rating: integer('rating').notNull(),
  comment: text('comment'),
  is_anonymous: boolean('is_anonymous').default(false),
  is_verified: boolean('is_verified').default(false),
  helpful_count: integer('helpful_count').default(0),
  created_at: timestamp('created_at', { withTimezone: true }).defaultNow(),
});

// Legacy 타입들
/**
 * @deprecated
 */
export type ClassTemplate = typeof class_templates.$inferSelect;
/**
 * @deprecated
 */
export type LegacyClassScheduleGroup = typeof legacy_class_schedule_groups.$inferSelect;
/**
 * @deprecated
 */
export type ClassOccurrence = typeof class_occurrences.$inferSelect;
/**
 * @deprecated
 */
export type ClassEnrollment = typeof class_enrollments.$inferSelect;
/**
 * @deprecated
 */
export type ClassAttendance = typeof class_attendances.$inferSelect;
/**
 * @deprecated
 */
export type ClassReview = typeof class_reviews.$inferSelect;

// Legacy enum 상수들 (TODO: 제거 예정)
export const OccurrenceStatusText = {
  SCHEDULED: 'scheduled',
  ONGOING: 'ongoing', // Legacy API 호환성
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

export const EnrollmentStatusText = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',  
  CANCELLED: 'cancelled',
} as const;

export const AttendanceStatusText = {
  PRESENT: 'present',
  ABSENT: 'absent',
  LATE: 'late',
  EXCUSED: 'excused', // Legacy API 호환성
} as const;

export type OccurrenceStatus = (typeof OccurrenceStatusText)[keyof typeof OccurrenceStatusText];
export type EnrollmentStatus = (typeof EnrollmentStatusText)[keyof typeof EnrollmentStatusText];
export type AttendanceStatus = (typeof AttendanceStatusText)[keyof typeof AttendanceStatusText];

// ===== RELATIONS 정의 =====

/**
 * 회원 관계 정의
 * - member_preferences와 1:1 관계 (학생 선호도)
 */
export const membersRelations = relations(members, ({ one, many }) => ({
  preferences: one(member_preferences, {
    fields: [members.id],
    references: [member_preferences.member_id],
  }),
}));

/**
 * 회원 선호도 관계 정의
 * - members와 1:1 관계
 */
export const memberPreferencesRelations = relations(
  member_preferences,
  ({ one }) => ({
    member: one(members, {
      fields: [member_preferences.member_id],
      references: [members.id],
    }),
  })
);





// ===== 클래스 도메인 관계 정의 =====

/**
 * 스튜디오 관계 정의
 * - classes와 1:N 관계
 * - instructors와 1:N 관계
 */
export const studiosRelations = relations(studios, ({ many }) => ({
  classes: many(classes),
  instructors: many(instructors),
}));

/**
 * 클래스 관계 정의
 * - partners와 N:1 관계
 * - studios와 N:1 관계
 * - instructors와 N:1 관계
 * - class_schedule_groups와 1:N 관계
 */
export const classesRelations = relations(
  classes,
  ({ one, many }) => ({
    partner: one(partners, {
      fields: [classes.partner_id],
      references: [partners.id],
    }),
    studio: one(studios, {
      fields: [classes.studio_id],
      references: [studios.id],
    }),
    instructor: one(instructors, {
      fields: [classes.instructor_id],
      references: [instructors.id],
    }),
    scheduleGroups: many(class_schedule_groups),
  })
);

/**
 * 클래스 스케줄 그룹 관계 정의
 * - classes와 N:1 관계
 * - class_schedules와 1:N 관계
 */
export const classScheduleGroupsRelations = relations(
  class_schedule_groups,
  ({ one, many }) => ({
    class: one(classes, {
      fields: [class_schedule_groups.class_id],
      references: [classes.id],
    }),
    schedules: many(class_schedules),
  })
);

/**
 * 클래스 스케줄 관계 정의
 * - class_schedule_groups와 N:1 관계
 */
export const classSchedulesRelations = relations(
  class_schedules,
  ({ one }) => ({
    scheduleGroup: one(class_schedule_groups, {
      fields: [class_schedules.schedule_group_id],
      references: [class_schedule_groups.id],
    }),
  })
);





// ===== 파트너 관계 정의 =====

/**
 * 파트너 관계 정의
 * - studios와 1:N 관계
 * - instructors와 1:N 관계
 * - classes와 1:N 관계
 */
export const partnersRelations = relations(partners, ({ many }) => ({
  studios: many(studios),
  instructors: many(instructors),
  classes: many(classes),
}));

/**
 * 강사 관계 정의
 * - studios와 N:1 관계
 * - partners와 N:1 관계
 * - classes와 1:N 관계
 */
export const instructorsRelations = relations(instructors, ({ one, many }) => ({
  studio: one(studios, {
    fields: [instructors.studioId],
    references: [studios.id],
  }),
  partner: one(partners, {
    fields: [instructors.partnerId],
    references: [partners.id],
  }),
  classes: many(classes),
}));
