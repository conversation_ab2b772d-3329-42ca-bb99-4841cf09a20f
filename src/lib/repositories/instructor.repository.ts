import { db } from '@/lib/db';
import { instructors, studios } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import type { 
  CreateInstructorInput,
  UpdateInstructorInput,
  InstructorLinks,
  InstructorImage,
  Specialty,
  Certificate,
} from '@/lib/schemas/instructor';

export class InstructorRepository {
  /**
   * 새로운 강사 생성
   */
  async create(data: CreateInstructorInput & { partnerId: string }): Promise<typeof instructors.$inferSelect> {
    const [instructor] = await db
      .insert(instructors)
      .values({
        studioId: data.studioId,
        partnerId: data.partnerId,
        name: data.name,
        gender: data.gender,
        contact: data.contact,
        description: data.description,
        links: data.links as InstructorLinks,
        profileImages: data.profileImages as InstructorImage[],
        experienceTotalYears: data.experienceTotalYears,
        specialties: data.specialties as Specialty[],
        certificates: data.certificates as Certificate[],
      })
      .returning();

    return instructor;
  }

  /**
   * ID와 스튜디오로 강사 조회
   */
  async findByIdAndStudio(
    instructorId: string,
    studioId: string
  ): Promise<typeof instructors.$inferSelect | null> {
    const [instructor] = await db
      .select()
      .from(instructors)
      .where(
        and(
          eq(instructors.id, instructorId),
          eq(instructors.studioId, studioId)
        )
      )
      .limit(1);

    return instructor || null;
  }

  /**
   * 스튜디오별 강사 목록 조회
   */
  async findByStudio(studioId: string): Promise<typeof instructors.$inferSelect[]> {
    const studioInstructors = await db
      .select()
      .from(instructors)
      .where(
        and(
          eq(instructors.studioId, studioId),
          eq(instructors.status, 'active')
        )
      )
      .orderBy(instructors.createdAt);

    return studioInstructors;
  }

  /**
   * 강사 정보 수정
   */
  async update(
    instructorId: string,
    data: UpdateInstructorInput
  ): Promise<typeof instructors.$inferSelect | null> {
    const updateData: any = {
      ...data,
      updatedAt: new Date(),
    };

    // JSON 필드들 타입 캐스팅
    if (data.links) updateData.links = data.links as InstructorLinks;
    if (data.profileImages) updateData.profileImages = data.profileImages as InstructorImage[];
    if (data.specialties) updateData.specialties = data.specialties as Specialty[];
    if (data.certificates) updateData.certificates = data.certificates as Certificate[];

    const [updated] = await db
      .update(instructors)
      .set(updateData)
      .where(eq(instructors.id, instructorId))
      .returning();

    return updated || null;
  }

  /**
   * 강사 소프트 삭제
   */
  async softDelete(instructorId: string): Promise<boolean> {
    const [deleted] = await db
      .update(instructors)
      .set({
        status: 'deleted',
        updatedAt: new Date(),
      })
      .where(eq(instructors.id, instructorId))
      .returning();

    return !!deleted;
  }

  /**
   * 강사 존재 여부 확인 (성능 최적화)
   */
  async checkExists(
    instructorId: string,
    studioId: string
  ): Promise<boolean> {
    const result = await db
      .select({ id: instructors.id })
      .from(instructors)
      .where(
        and(
          eq(instructors.id, instructorId),
          eq(instructors.studioId, studioId),
          eq(instructors.status, 'active')
        )
      )
      .limit(1);

    return result.length > 0;
  }

  /**
   * 스튜디오 소유권 확인을 위한 스튜디오 정보 조회
   */
  async findStudioOwnership(studioId: string): Promise<{ partnerId: string } | null> {
    const [studio] = await db
      .select({ partnerId: studios.partnerId })
      .from(studios)
      .where(eq(studios.id, studioId))
      .limit(1);

    return studio || null;
  }
}