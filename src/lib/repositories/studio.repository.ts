import { db } from '@/lib/db';
import { studios } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import type { CreateStudioRequest, UpdateStudioRequest } from '@/lib/schemas/studio';

export class StudioRepository {
  /**
   * 새로운 스튜디오 생성
   */
  async create(data: CreateStudioRequest): Promise<typeof studios.$inferSelect> {
    const [studio] = await db
      .insert(studios)
      .values({
        ...data,
        latitude: data.latitude?.toString(),
        longitude: data.longitude?.toString(),
        type: 'fitness', // 기본값으로 설정
      })
      .returning();

    return studio;
  }

  /**
   * ID와 파트너 ID로 스튜디오 조회
   */
  async findByIdAndPartnerId(id: string, partnerId: string): Promise<typeof studios.$inferSelect | null> {
    const [studio] = await db
      .select()
      .from(studios)
      .where(and(eq(studios.id, id), eq(studios.partnerId, partnerId)))
      .limit(1);

    return studio || null;
  }

  /**
   * 스튜디오 정보 업데이트
   */
  async update(
    id: string,
    partnerId: string,
    data: UpdateStudioRequest
  ): Promise<typeof studios.$inferSelect | null> {
    const updateData: any = {
      ...data,
      updatedAt: new Date(),
    };

    // latitude, longitude가 있으면 문자열로 변환
    if (data.latitude !== undefined) {
      updateData.latitude = data.latitude.toString();
    }
    if (data.longitude !== undefined) {
      updateData.longitude = data.longitude.toString();
    }

    const [updatedStudio] = await db
      .update(studios)
      .set(updateData)
      .where(and(eq(studios.id, id), eq(studios.partnerId, partnerId)))
      .returning();

    return updatedStudio || null;
  }
}