import { InstructorRepository } from '@/lib/repositories/instructor.repository';
import { type Instructor } from '@/lib/db/schema';
import {
  type CreateInstructorInput,
  type UpdateInstructorInput,
} from '@/lib/schemas/instructor';

export class InstructorService {
  private instructorRepository: InstructorRepository;

  constructor() {
    this.instructorRepository = new InstructorRepository();
  }

  /**
   * 스튜디오 소유권 검증 (공통 메서드)
   */
  private async validateStudioOwnership(
    studioId: string,
    currentPartnerId: string
  ): Promise<void> {
    const studio = await this.instructorRepository.findStudioOwnership(studioId);

    if (!studio) {
      throw new Error('존재하지 않는 스튜디오입니다');
    }

    if (studio.partnerId !== currentPartnerId) {
      throw new Error('해당 스튜디오에 대한 권한이 없습니다');
    }
  }

  /**
   * 강사 생성 (권한 검증 포함)
   */
  async createInstructor(
    data: CreateInstructorInput,
    currentPartnerId: string
  ): Promise<Instructor> {
    try {
      // 스튜디오 소유권 검증
      await this.validateStudioOwnership(data.studioId, currentPartnerId);

      // 강사 생성
      const instructor = await this.instructorRepository.create({
        ...data,
        partnerId: currentPartnerId,
      });

      return instructor;
    } catch (error) {
      console.error('Error creating instructor:', error);
      if (error instanceof Error) {
        throw error; // 권한 에러 등은 그대로 전파
      }
      throw new Error('강사 생성에 실패했습니다');
    }
  }

  /**
   * ID로 강사 조회 (권한 검증 포함)
   */
  async findInstructorById(
    instructorId: string,
    studioId: string,
    currentPartnerId: string
  ): Promise<Instructor | null> {
    try {
      // 스튜디오 소유권 검증
      await this.validateStudioOwnership(studioId, currentPartnerId);

      // 강사 조회 및 스튜디오 소속 확인
      const instructor = await this.instructorRepository.findByIdAndStudio(
        instructorId,
        studioId
      );

      return instructor;
    } catch (error) {
      console.error('Error fetching instructor by id:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 조회에 실패했습니다');
    }
  }

  /**
   * 강사 정보 수정 (권한 검증 포함)
   */
  async updateInstructor(
    instructorId: string,
    studioId: string,
    currentPartnerId: string,
    data: UpdateInstructorInput
  ): Promise<Instructor | null> {
    try {
      // 스튜디오 소유권 검증
      await this.validateStudioOwnership(studioId, currentPartnerId);

      // 강사가 해당 스튜디오 소속인지 확인
      const instructorExists = await this.instructorRepository.checkExists(
        instructorId,
        studioId
      );

      if (!instructorExists) {
        return null;
      }

      // 강사 정보 수정
      const updated = await this.instructorRepository.update(instructorId, data);

      return updated;
    } catch (error) {
      console.error('Error updating instructor:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 정보 수정에 실패했습니다');
    }
  }

  /**
   * 스튜디오별 강사 목록 조회 (권한 검증 포함)
   */
  async findInstructorsByStudio(
    studioId: string,
    currentPartnerId: string
  ): Promise<Instructor[]> {
    try {
      // 스튜디오 소유권 검증
      await this.validateStudioOwnership(studioId, currentPartnerId);

      // 스튜디오별 강사 목록 조회
      const studioInstructors = await this.instructorRepository.findByStudio(studioId);

      return studioInstructors;
    } catch (error) {
      console.error('Error fetching instructors by studio:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('스튜디오 강사 목록 조회에 실패했습니다');
    }
  }

  /**
   * 강사 삭제 (권한 검증 포함, 소프트 삭제)
   */
  async deleteInstructor(
    instructorId: string,
    studioId: string,
    currentPartnerId: string
  ): Promise<boolean> {
    try {
      // 스튜디오 소유권 검증
      await this.validateStudioOwnership(studioId, currentPartnerId);

      // 강사가 해당 스튜디오 소속인지 확인
      const instructorExists = await this.instructorRepository.checkExists(
        instructorId,
        studioId
      );

      if (!instructorExists) {
        return false;
      }

      // 강사 소프트 삭제
      const success = await this.instructorRepository.softDelete(instructorId);

      return success;
    } catch (error) {
      console.error('Error deleting instructor:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('강사 삭제에 실패했습니다');
    }
  }
}