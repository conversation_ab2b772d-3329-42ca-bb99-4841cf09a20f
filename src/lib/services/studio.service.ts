import { StudioRepository } from '@/lib/repositories/studio.repository';
import { studios } from '@/lib/db/schema';
import type { 
  CreateStudioRequest, 
  UpdateStudioRequest,
  StudioResponse,
  StudioImages,
  StudioLinks,
} from '@/lib/schemas/studio';

export type CreateStudioData = CreateStudioRequest;

export type UpdateStudioData = UpdateStudioRequest;


export class StudioService {
  private studioRepository: StudioRepository;

  constructor() {
    this.studioRepository = new StudioRepository();
  }

  /**
   * 새로운 스튜디오 생성
   */
  async createStudio(data: CreateStudioData): Promise<StudioResponse> {
    // 비즈니스 검증
    await this.validateStudioData(data);

    // 스튜디오 생성
    const studio = await this.studioRepository.create(data);
    
    return this.formatStudioResponse(studio);
  }

  /**
   * 스튜디오 조회 (파트너 ID 조건 포함)
   */
  async getStudioByIdAndPartnerId(id: string, partnerId: string): Promise<StudioResponse | null> {
    // UUID 형식 검증
    if (!this.isValidUUID(id)) {
      throw new Error('올바르지 않은 스튜디오 ID 형식입니다.');
    }
    if (!this.isValidUUID(partnerId)) {
      throw new Error('올바르지 않은 파트너 ID 형식입니다.');
    }

    const studio = await this.studioRepository.findByIdAndPartnerId(id, partnerId);
    
    if (!studio) {
      return null;
    }

    return this.formatStudioResponse(studio);
  }

  /**
   * 스튜디오 수정 (파트너 ID 조건 포함)
   */
  async updateStudio(id: string, partnerId: string, data: UpdateStudioData): Promise<StudioResponse | null> {
    // UUID 형식 검증
    if (!this.isValidUUID(id)) {
      throw new Error('올바르지 않은 스튜디오 ID 형식입니다.');
    }
    if (!this.isValidUUID(partnerId)) {
      throw new Error('올바르지 않은 파트너 ID 형식입니다.');
    }

    // 기존 스튜디오 존재 확인
    const existingStudio = await this.studioRepository.findByIdAndPartnerId(id, partnerId);
    if (!existingStudio) {
      throw new Error('스튜디오를 찾을 수 없습니다.');
    }

    // 비즈니스 검증 (수정 데이터에 대해)
    if (Object.keys(data).length > 0) {
      await this.validateStudioData(data);
    }

    // 스튜디오 업데이트
    const updatedStudio = await this.studioRepository.update(id, partnerId, data);
    
    if (!updatedStudio) {
      throw new Error('스튜디오 수정에 실패했습니다.');
    }

    return this.formatStudioResponse(updatedStudio);
  }

  /**
   * 스튜디오 데이터 검증
   */
  private async validateStudioData(
    data: Partial<CreateStudioData>
  ): Promise<void> {
    // 스튜디오 이름 검증
    if (data.name) {
      if (data.name.length < 2) {
        throw new Error('스튜디오 이름은 2자 이상 입력해주세요.');
      }
      if (data.name.length > 100) {
        throw new Error('스튜디오 이름은 100자 이하로 입력해주세요.');
      }
    }

    // 주소 검증
    if (data.address) {
      if (data.address.length < 5) {
        throw new Error('주소는 5자 이상 입력해주세요.');
      }
      if (data.address.length > 200) {
        throw new Error('주소는 200자 이하로 입력해주세요.');
      }
    }

    // 설명 검증
    if (data.description && data.description.length > 1000) {
      throw new Error('스튜디오 설명은 1000자 이하로 입력해주세요.');
    }

    // 좌표 검증
    if (data.latitude !== undefined) {
      const lat = Number(data.latitude);
      if (isNaN(lat) || lat < -90 || lat > 90) {
        throw new Error('올바른 위도 값을 입력해주세요. (-90 ~ 90)');
      }
    }

    if (data.longitude !== undefined) {
      const lng = Number(data.longitude);
      if (isNaN(lng) || lng < -180 || lng > 180) {
        throw new Error('올바른 경도 값을 입력해주세요. (-180 ~ 180)');
      }
    }

  }

  /**
   * UUID 형식 검증
   */
  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  /**
   * URL 형식 검증
   */
  private isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 스튜디오 응답 형식 변환
   */
  private formatStudioResponse(studio: typeof studios.$inferSelect): StudioResponse {
    return {
      id: studio.id,
      name: studio.name,
      phone: studio.phone,
      description: studio.description || undefined,
      address: studio.address,
      addressDetail: studio.addressDetail || undefined,
      postalCode: studio.postalCode || undefined,
      latitude: Number(studio.latitude),
      longitude: Number(studio.longitude),
      nearestStation: studio.nearestStation || undefined,
      stationDistance: studio.stationDistance || undefined,
      amenities: studio.amenities || undefined,
      operatingHours: (studio.operatingHours as any) || undefined,
      images: (studio.images as StudioImages) || undefined, // JSONB 구조로 변경
      links: (studio.links as StudioLinks) || undefined, // JSON 구조로 변경
      status: studio.status as 'active' | 'inactive' | 'pending' | 'closed',
      createdAt: studio.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: studio.updatedAt?.toISOString() || new Date().toISOString(),
    };
  }
}