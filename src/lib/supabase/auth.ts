'use client';

import { createBrowserClient } from '@supabase/ssr';
import { BASE_URL } from '../api/api.config';

/**
 * Supabase 브라우저 클라이언트
 * 클라이언트 컴포넌트에서 인증 관련 작업에 사용
 */
export const supabase = createBrowserClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);

/**
 * 카카오 로그인 함수
 *
 * @param redirectTo 로그인 성공 후 리다이렉트할 경로 (기본값: '/')
 * @returns Promise<AuthResponse> Supabase 인증 응답
 *
 * @example
 * // 기본 사용법 (홈으로 리다이렉트)
 * await signInWithKakao();
 *
 * @example
 * // 특정 페이지로 리다이렉트
 * await signInWithKakao('/dashboard');
 *
 * @example
 * // 컴포넌트에서 사용
 * import { signInWithKakao } from '@/lib/supabase/auth';
 *
 * const handleLogin = async () => {
 *   try {
 *     await signInWithKakao('/instructor/dashboard');
 *   } catch (error) {
 *     console.error('로그인 실패:', error);
 *   }
 * };
 */
export async function signInWithKakao(redirectTo: string = '/') {
  try {
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: 'kakao',
      options: {
        redirectTo: `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`,
      },
    });

    if (error) {
      console.error('카카오 로그인 오류:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('카카오 로그인 실패:', error);
    throw error;
  }
}

/**
 * 로그아웃 함수
 *
 * @example
 * // 컴포넌트에서 사용
 * import { signOut } from '@/lib/supabase/auth';
 *
 * const handleLogout = async () => {
 *   try {
 *     await signOut(); // 자동으로 홈페이지로 리다이렉트
 *   } catch (error) {
 *     console.error('로그아웃 실패:', error);
 *   }
 * };
 */
export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('로그아웃 오류:', error);
      throw error;
    }

    // 로그아웃 후 홈페이지로 리다이렉트
    window.location.href = '/';
  } catch (error) {
    console.error('로그아웃 실패:', error);
    throw error;
  }
}

/**
 * 현재 사용자 세션 가져오기
 *
 * @returns Promise<User | null> 현재 로그인된 사용자 정보 또는 null
 *
 * @example
 * // 사용자 정보 확인
 * const user = await getCurrentUser();
 * if (user) {
 *   console.log('로그인된 사용자:', user.email);
 * }
 */
export async function getCurrentUser() {
  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error) {
      console.error('사용자 정보 가져오기 오류:', error);
      return null;
    }

    return user;
  } catch (error) {
    console.error('사용자 정보 가져오기 실패:', error);
    return null;
  }
}

/**
 * 세션 변경 리스너
 *
 * @param callback 인증 상태 변경 시 호출될 콜백 함수
 * @returns 구독 해제 함수
 *
 * @example
 * // React 컴포넌트에서 사용
 * useEffect(() => {
 *   const { data: { subscription } } = onAuthStateChange((user) => {
 *     setUser(user);
 *   });
 *
 *   return () => subscription.unsubscribe();
 * }, []);
 */
export function onAuthStateChange(callback: (user: any) => void) {
  return supabase.auth.onAuthStateChange((_event, session) => {
    callback(session?.user || null);
  });
}

/**
 * 사용자가 로그인되어 있는지 확인
 */
export async function isAuthenticated(): Promise<boolean> {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * 현재 사용자의 역할 가져오기
 *
 * @returns Promise<string | null> 사용자 역할 ('STUDENT', 'INSTRUCTOR', 'ADMIN') 또는 null
 *
 * @example
 * // 사용자 역할 확인
 * const role = await getUserRole();
 * if (role === 'INSTRUCTOR') {
 *   router.push('/instructor/dashboard');
 * }
 */
export async function getUserRole(): Promise<string | null> {
  try {
    const response = await fetch(`${BASE_URL}/api/user/role`);

    if (!response.ok) {
      if (response.status === 401) {
        // 인증되지 않은 사용자
        return null;
      }
      throw new Error('역할 조회 실패');
    }

    const data = await response.json();
    return data.role;
  } catch (error) {
    console.error('사용자 역할 가져오기 실패:', error);
    return null;
  }
}

/**
 * 사용자 메타데이터 업데이트
 */
export async function updateUserMetadata(metadata: Record<string, any>) {
  try {
    const { data, error } = await supabase.auth.updateUser({
      data: metadata,
    });

    if (error) {
      console.error('사용자 메타데이터 업데이트 오류:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('사용자 메타데이터 업데이트 실패:', error);
    throw error;
  }
}
