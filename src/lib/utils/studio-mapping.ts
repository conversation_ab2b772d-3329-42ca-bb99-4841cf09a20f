import { CreateStudioRequest, StudioImageData } from '../api/partner/studios';

export interface StudioFormData {
  address: string;
  addressDetail?: string;
  zonecode?: string;
  latitude?: number;
  longitude?: number;
  name: string;
  description: string;
  phone?: string;
  websiteUrl?: string;
  snsUrl?: string;
  parkingAvailability?: string;
  parkingType?: string;
  shower?: string;
  exerciseClothing?: string;
  locker?: string;
  lockerPrice?: string;
  personalExercise?: string;
  towel?: string;
}

export interface CustomFacility {
  id: string;
  key: string;
  value: string;
}

/**
 * 폼 데이터를 API 요청 형식으로 변환합니다.
 */
export function mapFormDataToApiRequest(
  formData: StudioFormData,
  customFacilities: CustomFacility[],
  images: StudioImageData[]
): CreateStudioRequest {
  // 기본 시설 정보 매핑
  const amenities: Record<string, any> = {};

  // 주차장 정보
  if (formData.parkingAvailability) {
    amenities.parking = [{
      type: formData.parkingAvailability === 'available' 
        ? (formData.parkingType || 'free') 
        : 'none',
      description: formData.parkingAvailability === 'available'
        ? `${formData.parkingType === 'paid' ? '유료' : '무료'} 주차장 이용 가능`
        : '주차장 없음'
    }];
  }

  // 샤워실 정보
  if (formData.shower) {
    amenities.shower = [{
      available: formData.shower === 'available',
      description: formData.shower === 'available' ? '샤워실 완비' : '샤워실 없음'
    }];
  }

  // 운동복 정보
  if (formData.exerciseClothing) {
    amenities.exerciseClothing = [{
      type: formData.exerciseClothing,
      description: `운동복 ${formData.exerciseClothing === 'free' ? '무료' : '유료'} 제공`
    }];
  }

  // 락커룸 정보
  if (formData.locker) {
    amenities.locker = [{
      type: formData.locker,
      price: formData.locker === 'paid' && formData.lockerPrice 
        ? parseInt(formData.lockerPrice) 
        : undefined,
      description: formData.locker === 'free' 
        ? '무료 라커 이용 가능' 
        : `유료 라커 (${formData.lockerPrice || '0'}원/월)`
    }];
  }

  // 개인 추가 운동 정보
  if (formData.personalExercise) {
    amenities.personalExercise = [{
      available: formData.personalExercise === 'available',
      description: formData.personalExercise === 'available' 
        ? '개인 추가 운동 가능' 
        : '개인 추가 운동 불가'
    }];
  }

  // 운동 타월 정보
  if (formData.towel) {
    amenities.towel = [{
      type: formData.towel,
      description: `운동 타월 ${formData.towel === 'free' ? '무료' : '유료'} 제공`
    }];
  }

  // 커스텀 시설 정보 추가
  if (customFacilities.length > 0) {
    amenities.custom = customFacilities.map(facility => ({
      name: facility.key,
      description: facility.value
    }));
  }

  // API 요청 객체 생성
  const apiRequest: CreateStudioRequest = {
    name: formData.name,
    phone: formData.phone || '',
    address: formData.address,
    latitude: formData.latitude || 0,
    longitude: formData.longitude || 0,
  };

  // 선택 필드 추가
  if (formData.description) {
    apiRequest.description = formData.description;
  }

  if (formData.addressDetail) {
    apiRequest.addressDetail = formData.addressDetail;
  }

  if (formData.zonecode) {
    apiRequest.postalCode = formData.zonecode;
  }

  // 시설 정보가 있을 때만 추가
  if (Object.keys(amenities).length > 0) {
    apiRequest.amenities = amenities;
  }

  // 이미지 정보 추가
  if (images.length > 0) {
    apiRequest.images = images;
  }

  return apiRequest;
}

/**
 * 필수 필드 검증을 수행합니다.
 */
export function validateRequiredFields(formData: StudioFormData): string[] {
  const errors: string[] = [];

  if (!formData.name?.trim()) {
    errors.push('센터 이름을 입력해주세요.');
  }

  if (!formData.address?.trim()) {
    errors.push('센터 주소를 입력해주세요.');
  }

  if (!formData.description?.trim()) {
    errors.push('센터 소개를 입력해주세요.');
  }

  if (!formData.phone?.trim()) {
    errors.push('센터 연락처를 입력해주세요.');
  }

  if (!formData.latitude || !formData.longitude) {
    errors.push('주소 검색을 통해 정확한 위치를 설정해주세요.');
  }

  return errors;
}

/**
 * 이미지 파일들을 업로드하고 메타데이터를 반환합니다.
 */
export async function uploadStudioImages(
  imageFiles: File[],
  uploadFunction: (file: File, studioId?: string, prefix?: 'featured' | 'gallery') => Promise<{success: boolean, url: string, path: string}>
): Promise<StudioImageData[]> {
  const uploadResults: StudioImageData[] = [];

  for (let i = 0; i < imageFiles.length; i++) {
    const file = imageFiles[i];
    const prefix = i === 0 ? 'featured' : 'gallery';
    
    try {
      const result = await uploadFunction(file, undefined, prefix);
      if (result.success) {
        uploadResults.push({
          path: result.path,
          url: result.url
        });
      }
    } catch (error) {
      console.error(`이미지 업로드 실패 (${file.name}):`, error);
      throw new Error(`이미지 업로드에 실패했습니다: ${file.name}`);
    }
  }

  return uploadResults;
}