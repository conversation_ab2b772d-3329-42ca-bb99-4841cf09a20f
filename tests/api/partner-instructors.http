### Partner Instructors API 테스트 (파트너 인증 기반)

# 변수 설정
@baseUrl = http://localhost:3000
@contentType = application/json
@projectRef = dxodiiizyfzpueyvoaqr

# 파트너 인증을 위한 변수 설정
# 실제 테스트 시에는 브라우저에서 로그인 후 개발자 도구의 Application > Cookies에서
# sb-{{projectRef}}-auth-token 쿠키 값을 복사해서 사용하세요
@authToken = base64-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

# 테스트용 스튜디오 ID (실제 소유한 스튜디오 ID로 변경 필요)
@studioId = aa881146-755b-4311-a961-252d4abe4163

###############################################################################
# 인증 테스트 가이드
###############################################################################
# 
# 1. 브라우저에서 http://localhost:3000/partner/login 접속
# 2. 파트너 계정으로 로그인
# 3. 개발자 도구 (F12) 열기
# 4. Application > Storage > Cookies > localhost:3000
# 5. sb-{{projectRef}}-auth-token 쿠키 값 복사
# 6. 위의 @authToken 변수에 붙여넣기
# 7. @studioId를 실제 소유한 스튜디오 ID로 변경
# 8. 테스트 실행
#
# 주의사항:
# - 토큰은 보통 1시간 후 만료됩니다
# - 파트너 상태가 'ACTIVE'여야 API 사용 가능합니다
# - 자신이 소유한 스튜디오의 강사만 관리할 수 있습니다
###############################################################################

###
# 🔴 강사 등록 - 인증 없음 (401 에러)
POST {{baseUrl}}/api/partner/studios/{{studioId}}/instructors
Content-Type: {{contentType}}

{
  "name": "인증 없는 강사",
  "gender": "female",
  "description": "인증 없는 강사 등록 테스트",
  "experienceTotalYears": 5,
  "specialties": [
    {
      "type": "요가",
      "years": 5
    }
  ]
}

###
# ✅ 강사 등록 - 정상 케이스 (요가 강사)
POST {{baseUrl}}/api/partner/studios/{{studioId}}/instructors
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "김요가",
  "gender": "female",
  "contact": "010-1234-5678",
  "description": "10년 경력의 전문 요가 강사입니다. 하타요가와 빈야사 요가를 전문으로 하며, 개인의 수준에 맞춘 맞춤형 수업을 진행합니다.",
  "links": {
    "website": "https://yogakim.com",
    "sns": "https://instagram.com/yogakim"
  },
  "profileImages": [
    {
      "path": "instructors/partner-id/studio-id/profile-12345.jpg",
      "url": "https://example.com/profile1.jpg"
    },
    {
      "path": "instructors/partner-id/studio-id/profile-12346.jpg",
      "url": "https://example.com/profile2.jpg"
    }
  ],
  "experienceTotalYears": 10,
  "specialties": [
    {
      "type": "요가",
      "years": 10
    },
    {
      "type": "필라테스",
      "years": 3
    }
  ],
  "certificates": [
    {
      "name": "요가 지도자 자격증 1급",
      "issuing_organization": "대한요가협회",
      "issue_date": "2015-03-15",
      "expiry_date": "2025-03-15",
      "certificate_number": "YOGA-2015-12345"
    },
    {
      "name": "필라테스 지도자 자격증",
      "issuing_organization": "한국필라테스협회",
      "issue_date": "2021-06-20"
    }
  ]
}

###
# ✅ 스튜디오별 강사 목록 조회
GET {{baseUrl}}/api/partner/studios/{{studioId}}/instructors
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 강사 상세 정보 조회 (위에서 생성된 강사 ID로 변경 필요)
GET {{baseUrl}}/api/partner/studios/{{studioId}}/instructors/ecf84083-6a4b-43eb-b48d-dabc01a97b44
Accept: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###
# ✅ 강사 정보 수정
PUT {{baseUrl}}/api/partner/studios/{{studioId}}/instructors/ecf84083-6a4b-43eb-b48d-dabc01a97b44
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "description": "15년 경력의 요가 마스터입니다. 다양한 요가 스타일을 통해 몸과 마음의 균형을 찾도록 도와드립니다.",
  "experienceTotalYears": 15,
  "certificates": [
    {
      "name": "요가 마스터 자격증",
      "issuing_organization": "국제요가협회",
      "issue_date": "2024-01-01"
    }
  ]
}

###
# ✅ 강사 비활성화 (소프트 삭제)
DELETE {{baseUrl}}/api/partner/studios/{{studioId}}/instructors/ecf84083-6a4b-43eb-b48d-dabc01a97b44
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

###############################################################################
# 에러 케이스 테스트
###############################################################################

###
# 🔴 강사 등록 - 유효성 검증 실패 (이름 누락)
POST {{baseUrl}}/api/partner/studios/{{studioId}}/instructors
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "gender": "male",
  "description": "강사 설명입니다.",
  "experienceTotalYears": 3,
  "specialties": [
    {
      "type": "수영",
      "years": 3
    }
  ]
}

###
# 🔴 강사 등록 - 유효성 검증 실패 (전문 분야 없음)
POST {{baseUrl}}/api/partner/studios/{{studioId}}/instructors
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "이수영",
  "gender": "female",
  "description": "수영 강사입니다.",
  "experienceTotalYears": 5,
  "specialties": []
}

###
# 🔴 다른 파트너의 스튜디오에 강사 등록 시도 (403 에러)
POST {{baseUrl}}/api/partner/studios/00000000-0000-0000-0000-000000000000/instructors
Content-Type: {{contentType}}
Cookie: sb-{{projectRef}}-auth-token={{authToken}}

{
  "name": "권한 없는 강사",
  "gender": "male",
  "description": "다른 파트너의 스튜디오에 등록 시도",
  "experienceTotalYears": 3,
  "specialties": [
    {
      "type": "헬스",
      "years": 3
    }
  ]
}